<c-row ngPreserveWhitespaces>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Collapse</strong>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">You can use a link or a button component.</p>
        <app-docs-example href="components/collapse">
          <a (click)="toggleCollapse(0)" cButton class="me-1" color="primary">Link</a>
          <button (click)="toggleCollapse(0)" cButton class="me-1" color="primary">
            Button
          </button>
          <div [visible]="collapses[0]" cCollapse>
            <c-card class="shadow mt-3">
              <c-card-body>
                Anim pariatur cliche reprehenderit, enim eiusmod high life
                accusamus terry richardson ad squid. Nihil anim keffiyeh
                helvetica, craft beer labore wes anderson cred nesciunt sapiente
                ea proident.
              </c-card-body>
            </c-card>
          </div>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Collapse</strong> <small>horizontal</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add the <code>horizontal</code> property to transition the width
          instead of height and set a width on the immediate child element.
        </p>
        <app-docs-example href="components/collapse">
          <button
            (click)="toggleCollapse(1)"
            cButton
            class="me-1 mb-3"
            color="primary"
            [attr.aria-expanded]="collapses[1]"
          >
            Button
          </button>
          <div style="min-height: 130px">
            <div
              [visible]="collapses[1]"
              cCollapse
              horizontal
              style="max-width: 260px"
            >
              <c-card class="shadow">
                <c-card-body style="width: 260px">
                  This is some placeholder content for a horizontal collapse.
                  It's hidden by default and shown when triggered.
                </c-card-body>
              </c-card>
            </div>
          </div>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Collapse</strong> <small> multi target</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          A <code>&lt;c-button&gt;</code> can show and hide multiple elements.
        </p>
        <app-docs-example href="components/collapse#multiple-targets">
          <button
            (click)="toggleCollapse(2)"
            cButton
            class="me-1"
            color="primary"
          >
            Toggle first element
          </button>
          <button
            (click)="toggleCollapse(3)"
            cButton
            class="me-1"
            color="primary"
          >
            Toggle second element
          </button>
          <button
            (click)="toggleCollapse(2); toggleCollapse(3)"
            cButton
            class="me-1"
            color="primary"
          >
            Toggle both
          </button>
          <c-row style="min-height: 130px">
            <c-col xs="6">
              <div [visible]="collapses[2]" cCollapse>
                <c-card class="mt-3">
                  <c-card-body>
                    Anim pariatur cliche reprehenderit, enim eiusmod high life
                    accusamus terry richardson ad squid. Nihil anim keffiyeh
                    helvetica, craft beer labore wes anderson cred nesciunt
                    sapiente ea proident.
                  </c-card-body>
                </c-card>
              </div>
            </c-col>
            <c-col xs="6">
              <div [visible]="collapses[3]" cCollapse>
                <c-card class="mt-3">
                  <c-card-body>
                    Anim pariatur cliche reprehenderit, enim eiusmod high life
                    accusamus terry richardson ad squid. Nihil anim keffiyeh
                    helvetica, craft beer labore wes anderson cred nesciunt
                    sapiente ea proident.
                  </c-card-body>
                </c-card>
              </div>
            </c-col>
          </c-row>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
