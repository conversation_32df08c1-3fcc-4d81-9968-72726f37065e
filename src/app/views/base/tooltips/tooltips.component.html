<c-row>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Tooltip</strong> <small>Basic example</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Hover over the links below to see tooltips:
        </p>
        <app-docs-example href="components/tooltip">
          <p class="text-body-secondary">
            Tight pants next level keffiyeh
            <a cTooltip="Tooltip text" [routerLink]="[]" [cTooltipVisible]="true"> you probably </a>
            haven&#39;theard of them. Photo booth beard raw denim letterpress vegan messenger
            bag stumptown. Farm-to-table seitan, mcsweeney&#39;s fixie sustainable quinoa 8-bit
            american apparel
            <a cTooltip="Tooltip text" [routerLink]="[]"> have a </a>
            terry richardson vinyl chambray. Beard stumptown, cardigans banh mi lomo
            thundercats. Tofu biodiesel williamsburg marfa, four loko mcsweeney&#39;&#39;s
            cleanse vegan chambray. A really ironic artisan
            <a cTooltip="Tooltip text" href> whatever keytar </a>
            scenester farm-to-table banksy Austin
            <a cTooltip="Tooltip text" href> twitter handle </a>
            freegan cred raw denim single-origin coffee viral.
          </p>
        </app-docs-example>
        <p class="text-body-secondary small">
          Hover over the buttons below to see the four tooltips directions: top, right, bottom,
          and left.
        </p>
        <app-docs-example href="components/tooltip">
          <button cButton cTooltip="Vivamus sagittis lacus vel augue laoreet rutrum faucibus." cTooltipPlacement="top"
                  class="me-1" color="secondary">Tooltip on
            top
          </button>
          <button cButton cTooltip="Vivamus sagittis lacus vel augue laoreet rutrum faucibus." cTooltipPlacement="right"
                  class="me-1" color="secondary">Tooltip
            on right
          </button>
          <button cButton cTooltip="Vivamus sagittis lacus vel augue laoreet rutrum faucibus."
                  cTooltipPlacement="bottom"
                  class="me-1" color="secondary">Tooltip
            on bottom
          </button>
          <button cButton [cTooltip]="tooltipContent" cTooltipPlacement="left"
                  class="me-1" color="secondary">Tooltip on left
            <ng-template #tooltipContent>
              Vivamus sagittis lacus vel augue laoreet rutrum <i cTextColor="warning">faucibus</i>.
            </ng-template>
          </button>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
