<c-row ngPreserveWhitespaces>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Carousel</strong> <small>Slide only</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">Here’s a carousel with slides</p>
        <app-docs-example href="components/carousel">
          <c-carousel (itemChange)="onItemChange($event)" [interval]="7000" transition="slide">
            <c-carousel-inner>
              @for (slide of slides[0]; track slide.src) {
                <c-carousel-item>
                  <img
                    [src]="slide.src"
                    alt="{{slide.title}}"
                    class="d-block w-100"
                    loading="lazy"
                  />
                </c-carousel-item>
              }
            </c-carousel-inner>
          </c-carousel>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Carousel</strong> <small>with controls</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Adding in the previous and next controls with <code>c-carousel-controls</code> component.
        </p>
        <app-docs-example href="components/carousel/#with-controls">
          <c-carousel (itemChange)="onItemChange($event)" [interval]="0">
            <c-carousel-inner>
              @for (slide of slides[0]; track slide.src) {
                <c-carousel-item>
                  <img
                    [src]="slide.src"
                    alt="{{slide.title}}"
                    class="d-block w-100"
                    loading="lazy"
                  />
                </c-carousel-item>
              }
            </c-carousel-inner>
            <c-carousel-control caption="Previous" direction="prev" i18n-caption></c-carousel-control>
            <c-carousel-control caption="Next" direction="next" i18n-caption></c-carousel-control>
          </c-carousel>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Carousel</strong> <small>with custom controls</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Adding in the previous and next controls with custom content of <code>c-carousel-controls</code> component.
        </p>
        <app-docs-example href="components/carousel/#with-controls">
          <c-carousel (itemChange)="onItemChange($event)" [interval]="0">
            <c-carousel-inner>
              @for (slide of slides[0]; track slide.src) {
                <c-carousel-item>
                  <img
                    [src]="slide.src"
                    alt="{{slide.title}}"
                    class="d-block w-100"
                    loading="lazy"
                  />
                </c-carousel-item>
              }
            </c-carousel-inner>
            <c-carousel-control [tabIndex]="0" direction="prev">
              <svg cIcon name="cil-chevron-left" size="3xl"></svg>
              <span class="visually-hidden">Previous</span>
            </c-carousel-control>
            <c-carousel-control [tabIndex]="0" direction="next">
              <svg cIcon name="cil-chevron-right" size="3xl"></svg>
              <span class="visually-hidden">Next</span>
            </c-carousel-control>
          </c-carousel>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Carousel</strong> <small>with indicators</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          You can attach the indicators to the carousel, lengthwise the controls, too.
        </p>
        <app-docs-example href="components/carousel/#with-indicators">
          <c-carousel (itemChange)="onItemChange($event)" [activeIndex]="1" [dark]="true" [interval]="5000"
                      direction="prev">
            <c-carousel-indicators></c-carousel-indicators>
            <c-carousel-inner>
              @for (slide of slides[0]; track slide.src) {
                <c-carousel-item>
                  <img
                    [src]="slide.src"
                    alt="{{slide.title}}"
                    class="d-block w-100"
                    loading="lazy"
                  />
                </c-carousel-item>
              }
            </c-carousel-inner>
          </c-carousel>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        Carousel with captions, controls and indicators
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          You can add captions to slides with the <code>&lt;c-carousel-caption&gt;</code> element
          within any <code>&lt;c-carousel-item&gt;</code>. They can be immediately hidden on
          smaller viewports, as shown below, with optional <a href="https://coreui.io/4.0/utilities/display">display
          utilities</a>.
          We hide them with <code>.d-none</code> and draw them back on medium-sized devices with
          <code>.d-md-block</code>.
        </p>
        <app-docs-example href="components/carousel/#with-captions">
          <c-carousel (itemChange)="onItemChange($event)" [interval]="3000" transition="slide" [wrap]="false">
            <c-carousel-indicators></c-carousel-indicators>
            <c-carousel-inner>
              @for (slide of slides[1]; track slide.src) {
                <c-carousel-item>
                  <img
                    alt="{{slide.title}}"
                    class="d-block w-100"
                    loading="lazy"
                    src="{{slide.src}}"
                  />
                  <c-carousel-caption class="d-none d-md-block">
                    <h3>{{ slide.title }}</h3>
                    <p>{{ slide.subtitle }}</p>
                  </c-carousel-caption>
                </c-carousel-item>
              }
            </c-carousel-inner>
            <c-carousel-control caption="Previous" direction="prev" i18n-caption></c-carousel-control>
            <c-carousel-control caption="Next" direction="next" i18n-caption></c-carousel-control>
          </c-carousel>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Carousel</strong> <small>Crossfade</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add <code>transition="crossfade"</code> to your carousel to animate slides
          with a fade transition instead of a slide.
        </p>
        <app-docs-example href="components/carousel/#crossfade">
          <c-carousel (itemChange)="onItemChange($event)" [animate]="false" [interval]="5000" transition="crossfade">
            <c-carousel-inner>
              @for (slide of slides[0]; track slide.src) {
                <c-carousel-item>
                  <img
                    [src]="slide.src"
                    alt="{{slide.title}}"
                    class="d-block w-100"
                    loading="lazy"
                  />
                  <c-carousel-caption class="d-none d-md-block">
                    <h3>{{ slide.title }}</h3>
                    <p>{{ slide.subtitle }}</p>
                  </c-carousel-caption>
                </c-carousel-item>
              }
            </c-carousel-inner>
            <c-carousel-control caption="Previous" direction="prev" i18n-caption></c-carousel-control>
            <c-carousel-control caption="Next" direction="next" i18n-caption></c-carousel-control>
          </c-carousel>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Carousel</strong> <small>Dark variant</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add <code>dark</code> property to the <code>c-carousel</code> for darker controls,
          indicators, and captions. Controls have been inverted from their default white fill
          with the <code>filter</code> CSS property. Captions and controls have additional Sass
          variables that customize the <code>color</code> and <code>background-color</code>.
        </p>
        <app-docs-example href="components/carousel/#dark-variant">
          <c-carousel (itemChange)="onItemChange($event)" [dark]="true" [interval]="3000" transition="slide">
            <c-carousel-indicators></c-carousel-indicators>
            <c-carousel-inner>
              @for (slide of slides[2]; track slide.src) {
                <c-carousel-item>
                  <img
                    [src]="slide.src"
                    alt="{{slide.title}}"
                    class="d-block w-100"
                    loading="lazy"
                  />
                  <c-carousel-caption class="d-none d-md-block">
                    <h3>{{ slide.title }}</h3>
                    <p>{{ slide.subtitle }}</p>
                  </c-carousel-caption>
                </c-carousel-item>
              }
            </c-carousel-inner>
            <c-carousel-control caption="Previous" direction="prev" i18n-caption></c-carousel-control>
            <c-carousel-control caption="Next" direction="next" i18n-caption></c-carousel-control>
          </c-carousel>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
