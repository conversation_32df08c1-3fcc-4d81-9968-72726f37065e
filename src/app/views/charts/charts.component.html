<c-row>
  <c-col xs="12">
    <app-docs-callout href="charts">
      CoreUI Angular wrapper component for Chart.js 4.4, the most popular charting library.
      <br>
    </app-docs-callout>
  </c-col>
  <c-col xs="12" lg="6">
    <c-card class="mb-4">
      <c-card-header>
        Bar Chart
      </c-card-header>
      <c-card-body>
        <c-chart type="bar" [data]="chartBarData" [options]="options" />
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12" lg="6">
    <c-card class="mb-4">
      <c-card-header>
        Line Chart
      </c-card-header>
      <c-card-body>
        <c-chart type="line" [data]="chartLineData" [options]="options" />
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
<c-row>
  <c-col xs="12" lg="6">
    <c-card class="mb-4">
      <c-card-header>
        Doughnut Chart
      </c-card-header>
      <c-card-body>
        <c-chart type="doughnut" [data]="chartDoughnutData" [options]="options" />
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12" lg="6">
    <c-card class="mb-4">
      <c-card-header>
        Pie Chart
      </c-card-header>
      <c-card-body>
        <c-chart type="pie" [data]="chartPieData" [options]="options" />
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
<c-row>
  <c-col xs="12" lg="6">
    <c-card class="mb-4">
      <c-card-header>
        Polar Area Chart
      </c-card-header>
      <c-card-body>
        <c-chart type="polarArea" [data]="chartPolarAreaData" [options]="options" />
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12" lg="6">
    <c-card class="mb-4">
      <c-card-header>
        Radar Chart
      </c-card-header>
      <c-card-body>
        <c-chart type="radar" [data]="chartRadarData" [options]="options" />
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
