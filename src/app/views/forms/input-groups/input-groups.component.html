<c-row>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Input group</strong> <small>Basic example</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Place one add-on or button on either side of an input. You may also place one on both
          sides of an input. Remember to place <code>&lt;label&gt;</code>s outside the
          input group.
        </p>
        <app-docs-example href="forms/input-group">
          <c-input-group class="mb-3">
            <span cInputGroupText id="basic-addon1">&#64;</span>
            <input aria-describedby="basic-addon1"
                   aria-label="Username"
                   cFormControl
                   placeholder="Username"
            />
          </c-input-group>
          <c-input-group class="mb-3">
            <input aria-describedby="basic-addon2"
                   aria-label="Recipient&#39;s username"
                   cFormControl
                   placeholder="Recipient&#39;s username"
            />
            <span cInputGroupText id="basic-addon2">&#64;example.com</span>
          </c-input-group>
          <label cLabel for="basic-url">Your vanity URL</label>
          <c-input-group class="mb-3">
            <span cInputGroupText id="basic-addon3">https://example.com/users/</span>
            <input aria-describedby="basic-addon3" cFormControl id="basic-url"/>
          </c-input-group>
          <c-input-group class="mb-3">
            <span cInputGroupText>$</span>
            <input aria-label="Amount (to the nearest dollar)" cFormControl/>
            <span cInputGroupText>.00</span>
          </c-input-group>
          <c-input-group class="mb-3">
            <input aria-label="Username" cFormControl placeholder="Username"/>
            <span cInputGroupText>&#64;</span>
            <input aria-label="Server" cFormControl placeholder="Server"/>
          </c-input-group>
          <c-input-group>
            <span cInputGroupText>With textarea</span>
            <textarea aria-label="With textarea" cFormControl></textarea>
          </c-input-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Input group</strong> <small>Wrapping</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Input groups wrap by default via <code>flex-wrap: wrap</code> in order to accommodate
          custom form field validation within an input group. You may disable this with
          <code>.flex-nowrap</code>.
        </p>
        <app-docs-example href="forms/input-group#wrapping">
          <c-input-group class="flex-nowrap">
            <span cInputGroupText id="addon-wrapping">&#64;</span>
            <input aria-describedby="addon-wrapping"
                   aria-label="Username"
                   cFormControl
                   placeholder="Username"
            />
          </c-input-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Input group</strong> <small>Sizing</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add the relative form sizing classes to the <code>&lt;c-input-group&gt;</code> itself
          and contents within will automatically resize—no need for repeating the form control
          size classes on each element.
        </p>
        <p class="text-body-secondary small">
          <strong>Sizing on the individual input group elements isn&#39;t supported.</strong>
        </p>
        <app-docs-example href="forms/input-group#sizing">
          <c-input-group class="mb-3" sizing="sm">
            <span cInputGroupText id="inputGroup-sizing-sm">Small</span>
            <input aria-describedby="inputGroup-sizing-sm"
                   aria-label="Sizing example input"
                   cFormControl
            />
          </c-input-group>
          <c-input-group class="mb-3">
            <span cInputGroupText id="inputGroup-sizing-default">Default</span>
            <input aria-describedby="inputGroup-sizing-default"
                   aria-label="Sizing example input"
                   cFormControl
            />
          </c-input-group>
          <c-input-group sizing="lg">
            <span cInputGroupText id="inputGroup-sizing-lg">Large</span>
            <input aria-describedby="inputGroup-sizing-lg"
                   aria-label="Sizing example input"
                   cFormControl
            />
          </c-input-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Input group</strong> <small>Checkboxes and radios</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Place any checkbox or radio option within an input group&#39;s addon instead of text.
        </p>
        <app-docs-example href="forms/input-group#checkboxes-and-radios">
          <c-input-group class="mb-3">
            <span cInputGroupText>
              <div>
                <input cFormCheckInput id="checkOne" type="checkbox"/>
              </div>
            </span>
            <input aria-label="Text input with checkbox" cFormControl/>
          </c-input-group>
          <c-input-group>
            <span cInputGroupText>
              <div>
                <input cFormCheckInput id="radioOne" type="radio"/>
              </div>
            </span>
            <input aria-label="Text input with radio button" cFormControl/>
          </c-input-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Input group</strong> <small>Multiple inputs</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          While multiple <code>&lt;CFormInput&gt;</code>s are supported visually, validation
          styles are only available for input groups with a single
          <code>cFormControl</code>.
        </p>
        <app-docs-example href="forms/input-group#multiple-inputs">
          <c-input-group>
            <span cInputGroupText>First and last name</span>
            <input aria-label="First name" cFormControl/>
            <input aria-label="Last name" cFormControl/>
          </c-input-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Input group</strong> <small>Multiple addons</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Multiple add-ons are supported and can be mixed with checkbox and radio input
          versions..
        </p>
        <app-docs-example href="forms/input-group#multiple-addons">
          <c-input-group class="mb-3">
            <span cInputGroupText>$</span>
            <span cInputGroupText>0.00</span>
            <input aria-label="Dollar amount (with dot and two decimal places)" cFormControl/>
          </c-input-group>
          <c-input-group>
            <input aria-label="Dollar amount (with dot and two decimal places)" cFormControl/>
            <span cInputGroupText>$</span>
            <span cInputGroupText>0.00</span>
          </c-input-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Input group</strong> <small>Button addons</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Button add-ons are supported.
        </p>
        <app-docs-example href="forms/input-group#button-addons">
          <c-input-group class="mb-3">
            <button cButton color="secondary" id="button-addon1" type="button" variant="outline">
              Button
            </button>
            <input aria-describedby="button-addon1"
                   aria-label="Example text with button addon"
                   cFormControl
                   placeholder=""
            />
          </c-input-group>
          <c-input-group class="mb-3">
            <input aria-describedby="button-addon2"
                   aria-label="Recipient's username"
                   cFormControl
                   placeholder="Recipient's username"
            />
            <button cButton color="secondary" id="button-addon2" type="button" variant="outline">
              Button
            </button>
          </c-input-group>
          <c-input-group class="mb-3">
            <button cButton color="secondary" type="button" variant="outline">
              Button
            </button>
            <button cButton color="secondary" type="button" variant="outline">
              Button
            </button>
            <input aria-label="Example text with two button addons" cFormControl placeholder=""/>
          </c-input-group>
          <c-input-group>
            <input aria-label="Recipient's username with two button addons"
                   cFormControl
                   placeholder="Recipient's username"
            />
            <button cButton color="secondary" type="button" variant="outline">
              Button
            </button>
            <button cButton color="secondary" type="button" variant="outline">
              Button
            </button>
          </c-input-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Input group</strong> <small>Buttons with dropdowns</small>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="forms/input-group#buttons-with-dropdowns">
          <c-input-group class="mb-3">
            <c-dropdown>
              <button cButton cDropdownToggle color="secondary" variant="outline">
                Dropdown
              </button>
              <ul cDropdownMenu>
                <li><a [routerLink]="[]" cDropdownItem>Action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Another action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Something else here</a></li>
                <li>
                  <hr cDropdownDivider>
                </li>
                <li><a [routerLink]="[]" cDropdownItem>Separated link</a></li>
              </ul>
            </c-dropdown>
            <input aria-label="Text input with dropdown button" cFormControl/>
          </c-input-group>
          <c-input-group class="mb-3">
            <input aria-label="Text input with dropdown button" cFormControl/>
            <c-dropdown alignment="end">
              <button cButton cDropdownToggle color="secondary" variant="outline">
                Dropdown
              </button>
              <ul cDropdownMenu>
                <li><a [routerLink]="[]" cDropdownItem>Action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Another action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Something else here</a></li>
                <li>
                  <hr cDropdownDivider>
                </li>
                <li><a [routerLink]="[]" cDropdownItem>Separated link</a></li>
              </ul>
            </c-dropdown>
          </c-input-group>
          <c-input-group>
            <c-dropdown>
              <button cButton cDropdownToggle color="secondary" variant="outline">
                Dropdown
              </button>
              <ul cDropdownMenu>
                <li><a [routerLink]="[]" cDropdownItem>Action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Another action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Something else here</a></li>
                <li>
                  <hr cDropdownDivider>
                </li>
                <li><a [routerLink]="[]" cDropdownItem>Separated link</a></li>
              </ul>
            </c-dropdown>
            <input aria-label="Text input with 2 dropdown buttons" cFormControl/>
            <c-dropdown alignment="end">
              <button cButton cDropdownToggle color="secondary" variant="outline">
                Dropdown
              </button>
              <ul cDropdownMenu>
                <li><a [routerLink]="[]" cDropdownItem>Other Action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Another action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Something else here</a></li>
                <li>
                  <hr cDropdownDivider>
                </li>
                <li><a [routerLink]="[]" cDropdownItem>Separated link</a></li>
              </ul>
            </c-dropdown>
          </c-input-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Input group</strong> <small>Segmented buttons</small>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="forms/input-group#segmented-buttons">
          <c-input-group class="mb-3">
            <c-dropdown>
              <button cButton color="secondary" type="button" variant="outline">
                Action
              </button>
              <button [split]="true" cButton cDropdownToggle color="secondary" variant="outline">
                <span class="visually-hidden">Toggle Dropdown</span>
              </button>
              <ul cDropdownMenu>
                <li><a [routerLink]="[]" cDropdownItem>Action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Another action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Something else here</a></li>
                <li>
                  <hr cDropdownDivider>
                </li>
                <li><a [routerLink]="[]" cDropdownItem>Separated link</a></li>
              </ul>
            </c-dropdown>
            <input aria-label="Text input with segmented dropdown button" cFormControl/>
          </c-input-group>
          <c-input-group class="mb-3">
            <input aria-label="Text input with segmented dropdown button" cFormControl/>
            <c-dropdown alignment="end">
              <button cButton color="secondary" variant="outline">
                Action
              </button>
              <button [split]="true" cButton cDropdownToggle color="secondary" variant="outline">
                <span class="visually-hidden">Toggle Dropdown</span>
              </button>
              <ul cDropdownMenu>
                <li><a [routerLink]="[]" cDropdownItem>Action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Another action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Something else here</a></li>
                <li>
                  <hr cDropdownDivider>
                </li>
                <li><a [routerLink]="[]" cDropdownItem>Separated link</a></li>
              </ul>
            </c-dropdown>
          </c-input-group>
          <c-input-group>
            <c-dropdown>
              <button cButton color="secondary" type="button" variant="outline">
                Action
              </button>
              <button [split]="true" cButton cDropdownToggle color="secondary" variant="outline">
                <span class="visually-hidden">Toggle Dropdown</span>
              </button>
              <ul cDropdownMenu>
                <li><a [routerLink]="[]" cDropdownItem>Action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Another action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Something else here</a></li>
                <li>
                  <hr cDropdownDivider>
                </li>
                <li><a [routerLink]="[]" cDropdownItem>Separated link</a></li>
              </ul>
            </c-dropdown>
            <input aria-label="Text input with segmented dropdown button" cFormControl/>
            <c-dropdown alignment="end">
              <button cButton color="secondary" variant="outline">
                Action
              </button>
              <button [split]="true" cButton cDropdownToggle color="secondary" variant="outline">
                <span class="visually-hidden">Toggle Dropdown</span>
              </button>
              <ul cDropdownMenu>
                <li><a [routerLink]="[]" cDropdownItem>Action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Another action</a></li>
                <li><a [routerLink]="[]" cDropdownItem>Something else here</a></li>
                <li>
                  <hr cDropdownDivider>
                </li>
                <li><a [routerLink]="[]" cDropdownItem>Separated link</a></li>
              </ul>
            </c-dropdown>
          </c-input-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Input group</strong> <small>Custom select</small>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="forms/input-group#custom-select">
          <c-input-group class="mb-3">
            <label cInputGroupText for="inputGroupSelect01">
              Options
            </label>
            <select cSelect id="inputGroupSelect01">
              <option>Choose...</option>
              <option value="1">One</option>
              <option value="2">Two</option>
              <option value="3">Three</option>
            </select>
          </c-input-group>
          <c-input-group class="mb-3">
            <select cSelect id="inputGroupSelect02">
              <option>Choose...</option>
              <option value="1">One</option>
              <option value="2">Two</option>
              <option value="3">Three</option>
            </select>
            <label cInputGroupText for="inputGroupSelect02">
              Options
            </label>
          </c-input-group>
          <c-input-group class="mb-3">
            <button cButton color="secondary" type="button" variant="outline">
              Button
            </button>
            <select aria-label="Example select with button addon" cSelect id="inputGroupSelect03">
              <option>Choose...</option>
              <option value="1">One</option>
              <option value="2">Two</option>
              <option value="3">Three</option>
            </select>
          </c-input-group>
          <c-input-group>
            <select aria-label="Example select with button addon" cSelect id="inputGroupSelect04">
              <option>Choose...</option>
              <option value="1">One</option>
              <option value="2">Two</option>
              <option value="3">Three</option>
            </select>
            <button cButton color="secondary" type="button" variant="outline">
              Button
            </button>
          </c-input-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Input group</strong> <small>Custom file input</small>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="forms/input-group#custom-file-input">
          <c-input-group class="mb-3">
            <label cInputGroupText for="inputGroupFile01">
              Upload
            </label>
            <input cFormControl id="inputGroupFile01" type="file"/>
          </c-input-group>
          <c-input-group class="mb-3">
            <input cFormControl id="inputGroupFile02" type="file"/>
            <label cInputGroupText for="inputGroupFile02">
              Upload
            </label>
          </c-input-group>
          <c-input-group class="mb-3">
            <button cButton
                    color="secondary"
                    id="inputGroupFileAddon03"
                    type="button"
                    variant="outline"
            >
              Button
            </button>
            <input aria-describedby="inputGroupFileAddon03"
                   aria-label="Upload"
                   cFormControl
                   id="inputGroupFile03"
                   type="file"
            />
          </c-input-group>
          <c-input-group>
            <input aria-describedby="inputGroupFileAddon04"
                   aria-label="Upload"
                   cFormControl
                   id="inputGroupFile04"
                   type="file"
            />
            <button cButton
                    color="secondary"
                    id="inputGroupFileAddon04"
                    type="button"
                    variant="outline"
            >
              Button
            </button>
          </c-input-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
