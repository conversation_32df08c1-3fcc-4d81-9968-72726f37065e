.prescriptions-container {
  padding: 1.5rem;
  background-color: #FFFFFF;
  min-height: 100vh;
  overflow: visible !important; // Ensure dropdowns can overflow

  // 🔷 New Header Layout matching mockup
  .prescriptions-header {
    margin-bottom: 2rem;
    overflow: visible !important; // Ensure dropdowns can overflow

    // Top Section: Title (left) and Icons (max right)
    .title-and-icons {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #163659;
        margin: 0;
      }

      .header-icons {
        display: flex;
        gap: 0.5rem;

        .icon-button {
          width: 40px;
          height: 40px;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          background: white;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #f0f0f0;
            border-color: #ccc;
          }

          .icon {
            width: 18px;
            height: 18px;
            color: #666;
          }

          &:hover .icon {
            color: #1061AC;
          }
        }
      }
    }

    // Filter Section: Background #ECEFF9
    .filter-container {
      background-color: #ECEFF9;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 1rem;
      overflow: visible !important; // Ensure dropdowns can overflow

      .filter-row {
        display: flex;
        gap: 2rem;
        align-items: end;
        overflow: visible !important; // Ensure dropdowns can overflow

        .filter-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          overflow: visible !important; // Ensure dropdowns can overflow

          .filter-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #163659;
          }

          // ✅ Custom dropdown with white background and square checkboxes
          .custom-dropdown {
            position: relative;
            min-width: 150px;

            .dropdown-toggle {
              width: 100%;
              padding: 0.5rem 0.75rem;
              border: 1px solid #C7DAEC;
              border-radius: 6px;
              background: white;
              font-size: 0.875rem;
              color: #163659;
              cursor: pointer;
              display: flex;
              justify-content: space-between;
              align-items: center;
              text-align: left;

              &:focus {
                outline: none;
                border-color: #1061AC;
                box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
              }


            }

            // ✅ White background dropdown menu
            .dropdown-menu {
              position: absolute;
              top: 100%;
              left: 0;
              right: 0;
              background: #FFFFFF !important;
              border: 2px solid #C7DAEC !important;
              border-radius: 8px;
              box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25) !important;
              z-index: 99999 !important;
              margin-top: 4px;
              min-width: 200px !important;
              max-height: 350px !important;
              overflow-y: auto;
              display: block !important;
              visibility: visible !important;

              .dropdown-item {
                display: flex !important;
                align-items: center;
                padding: 1rem 1.25rem !important;
                cursor: pointer;
                transition: all 0.2s ease;
                gap: 0.75rem;
                border-bottom: 1px solid #e0e0e0 !important;
                min-height: 48px !important;
                background-color: #FFFFFF !important;

                &:hover {
                  background-color: #F0F8FF !important;
                  border-left: 3px solid #1061AC !important;
                  padding-left: 1rem !important;
                }

                &:first-child {
                  border-radius: 8px 8px 0 0;
                }

                &:last-child {
                  border-radius: 0 0 8px 8px;
                  border-bottom: none !important;
                }

                // ✅ Custom rectangular checkbox - smaller and wider than tall
                .checkbox-square {
                  width: 18px !important;
                  height: 14px !important;
                  border: 2px solid #1061AC !important;
                  border-radius: 3px;
                  background: white !important;
                  position: relative;
                  flex-shrink: 0;
                  transition: all 0.2s ease;
                  display: inline-block !important;
                  margin-right: 0.75rem;

                  // ✅ Checkmark when selected - adjusted for smaller rectangular checkbox
                  &::after {
                    content: '';
                    position: absolute;
                    top: 1px;
                    left: 4px;
                    width: 4px;
                    height: 8px;
                    border: solid white;
                    border-width: 0 2px 2px 0;
                    transform: rotate(45deg);
                    opacity: 0;
                    transition: opacity 0.2s ease;
                  }

                  // ✅ When checked - Make it very visible
                  &.checked {
                    background-color: #1061AC !important;
                    border-color: #1061AC !important;
                    box-shadow: 0 0 0 1px #1061AC !important;

                    &::after {
                      opacity: 1 !important;
                    }
                  }
                }

                .option-text {
                  font-size: 1rem !important;
                  color: #000000 !important;
                  font-weight: 500 !important;
                  flex: 1;
                  line-height: 1.4;
                }
              }
            }
          }
        }
      }
    }

    // Second Row: Add Button (left) and Calendar (right)
    .action-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 2rem;

      .primary-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: #1061AC;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(16, 97, 172, 0.3);
        }

        .button-icon {
          width: 16px;
          height: 16px;
        }
      }

      .calendar-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .nav-arrow {
          width: 32px;
          height: 32px;
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          background: white;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #f0f0f0;
            border-color: #ccc;
          }

          svg {
            width: 16px;
            height: 16px;
            color: #666;
          }
        }

        .date-selector {
          display: flex;
          gap: 0.25rem;
          padding: 0.25rem;
          background: white;
          border-radius: 8px;
          border: 1px solid #e0e0e0;

          .date-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.5rem 0.75rem;
            border: none;
            border-radius: 6px;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 50px;

            &:hover {
              background-color: #f0f0f0;
            }

            &.active {
              background-color: #1061AC;
              color: white;
            }

            .day-name {
              font-size: 0.75rem;
              font-weight: 500;
              margin-bottom: 0.125rem;
            }

            .day-number {
              font-size: 0.875rem;
              font-weight: 600;
            }
          }
        }
      }
    }

    // 🧩 Responsive Design for Tablet Width
    @media (max-width: 768px) {
      .title-and-icons {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;

        .header-icons {
          align-self: flex-end; // Keep icons on the right even when stacked
        }
      }

      .filter-container .filter-row {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;

        .filter-group .filter-dropdown {
          min-width: 100%;
        }
      }

      .action-row {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;

        .primary-button {
          justify-content: center;
        }

        .calendar-container {
          justify-content: center;

          .date-selector {
            flex-wrap: wrap;
            justify-content: center;
          }
        }
      }
    }

    @media (max-width: 480px) {
      .calendar-container .date-selector {
        .date-button {
          min-width: 45px;
          padding: 0.375rem 0.5rem;

          .day-name {
            font-size: 0.6875rem;
          }

          .day-number {
            font-size: 0.8125rem;
          }
        }
      }
    }
  }
}

/* Custom Modal Styles */
::ng-deep .custom-modal {
  z-index: 10000 !important;

  .mat-mdc-dialog-container {
    padding: 0 !important;
    background: transparent !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    overflow: hidden !important;
    width: 900px !important;
    height: 400px !important;
    max-width: 900px !important;
    max-height: 400px !important;
    z-index: 10001 !important;
  }

  .mat-mdc-dialog-surface {
    padding: 0 !important;
    background: transparent !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    overflow: hidden !important;
    width: 900px !important;
    height: 400px !important;
    max-width: 900px !important;
    max-height: 400px !important;
    z-index: 10002 !important;
  }
}

::ng-deep .custom-backdrop {
  background: rgba(0, 0, 0, 0.5) !important;
  z-index: 9999 !important;
}

/* Ensure the dialog overlay has high z-index */
::ng-deep .cdk-overlay-container {
  z-index: 10000 !important;
}

::ng-deep .cdk-global-overlay-wrapper {
  z-index: 10000 !important;
}

::ng-deep .cdk-overlay-pane {
  z-index: 10001 !important;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;

  .header-left {
    .page-title {
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 1rem;
    }

    .add-button {
      background-color: #1061AC;
      border: none;
      border-radius: 8px;
      color: white;
      padding: 0.5rem 1.25rem;
      font-weight: 500;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;

    .date-range-selector {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background: white;
      border-radius: 12px;
      padding: 0.5rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .nav-arrow {
        background: none;
        border: none;
        padding: 0.5rem;
        cursor: pointer;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: #f0f0f0;
        }

        svg {
          width: 16px;
          height: 16px;
          color: #666;
        }
      }

      .date-items {
        display: flex;
        gap: 0.25rem;

        .date-item {
          padding: 0.5rem 0.75rem;
          border-radius: 8px;
          cursor: pointer;
          text-align: center;
          min-width: 50px;
          transition: all 0.2s ease;

          &:hover {
            background-color: #f0f0f0;
          }

          &.active {
            background-color: #1061AC;
            color: white;
          }

          .day-name {
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
          }

          .day-number {
            font-size: 1rem;
            font-weight: bold;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 0.5rem;

      .tooltip-container {
        position: relative;
        display: inline-block;
      }

      .action-btn {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 0.75rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f0f0f0;
          border-color: #ccc;
        }

        svg {
          width: 18px;
          height: 18px;
          color: #666;
        }
      }

      .tooltip {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 8px;
        padding: 6px 10px;
        background: white;
        color: #333;
        font-size: 12px;
        font-weight: 500;
        border-radius: 4px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.2s ease;
        z-index: 1000;
        pointer-events: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        border: 1px solid #e0e0e0;

        /* Tooltip arrow */
        &::before {
          content: '';
          position: absolute;
          bottom: 100%;
          left: 50%;
          transform: translateX(-50%);
          border: 4px solid transparent;
          border-bottom-color: white;
        }

        /* Tooltip arrow border */
        &::after {
          content: '';
          position: absolute;
          bottom: 100%;
          left: 50%;
          transform: translateX(-50%);
          border: 5px solid transparent;
          border-bottom-color: #e0e0e0;
          z-index: -1;
        }
      }

      .tooltip-container:hover .tooltip {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(2px);
      }
    }
  }
}

.table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .prescriptions-table {
    width: 100%;
    border-collapse: collapse;

    thead {
      background-color: #f8f9fa;

      th {
        padding: 1rem;
        text-align: left;
        font-weight: 600;
        color: #333;
        border-bottom: 1px solid #e0e0e0;
        font-size: 0.875rem;
      }
    }

    tbody {
      tr {
        border-bottom: 1px solid #f0f0f0;

        &:hover {
          background-color: #f9f9f9;
        }

        td {
          padding: 1rem;
          vertical-align: middle;
          font-size: 0.875rem;

          &.patient-cell {
            .patient-info {
              display: flex;
              align-items: center;
              gap: 0.75rem;

              .initials-badge {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background-color: #E3F2FD;
                color: #1976D2;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                font-size: 0.875rem;
              }
            }
          }

          .empty-state {
            background-color: #F5FFF9;
            color: #4CAF50;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
          }

          .badge {
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;

            &.badge-priority {
              background-color: #FFEBEE;
              color: #C62828;
            }

            &.badge-cancelled {
              background-color: #FFCDD2;
              color: #D32F2F;
            }

            &.badge-in-progress {
              background-color: #FFF3E0;
              color: #F57C00;
            }

            // New prescription status badges
            &.badge-pending {
              background-color: #FFF3E0;
              color: #F57C00;
            }

            &.badge-approved {
              background-color: #E8F5E8;
              color: #2E7D32;
            }

            &.badge-rejected {
              background-color: #FFCDD2;
              color: #D32F2F;
            }

            &.badge-expired {
              background-color: #F5F5F5;
              color: #757575;
            }

            &.badge-uploaded {
              background-color: #E8F5E8;
              color: #2E7D32;
            }

            &.badge-default {
              background-color: #E3F2FD;
              color: #1976D2;
            }

            // Patient status badges
            &.badge-active {
              background-color: #E8F5E8;
              color: #2E7D32;
            }

            &.badge-hospitalized {
              background-color: #FFF3E0;
              color: #F57C00;
            }

            &.badge-vacation {
              background-color: #E3F2FD;
              color: #1976D2;
            }

            // Pharmacy status badges
            &.badge-open {
              background-color: #E8F5E8;
              color: #2E7D32;
            }

            &.badge-closed {
              background-color: #FFCDD2;
              color: #D32F2F;
            }
          }

          &.actions-cell {
            button {
              background: none;
              border: none;
              padding: 0.5rem;
              margin: 0 0.25rem;
              cursor: pointer;
              border-radius: 4px;
              display: inline-flex;
              align-items: center;
              justify-content: center;

              &:hover {
                background-color: #f0f0f0;
              }

              .eye-icon {
                width: 18px;
                height: 18px;
              }

              svg {
                width: 18px;
                height: 18px;
                color: #666;
              }
            }
          }
        }
      }
    }
  }
}

/* Cards View */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 0;
}

.prescription-card {
  background: #FFFFFF;
  border: 1px solid #C7DAEC;
  border-radius: 10px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    .patient-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      flex: 1;

      .initials-badge {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background-color: #E3F2FD;
        color: #1976D2;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
        flex-shrink: 0;
      }

      .patient-details {
        flex: 1;

        .patient-name {
          font-size: 1rem;
          font-weight: 700;
          color: #333;
          margin: 0 0 0.25rem 0;
          line-height: 1.2;
        }

        .patient-foyer,
        .patient-pharmacy {
          font-size: 0.875rem;
          margin: 0;
          font-weight: 400;
          display: flex;
          gap: 0.5rem;

          .foyer-label,
          .pharmacy-label {
            color: #728A9B;
            font-weight: 500;
            flex-shrink: 0;
          }

          .foyer-value,
          .pharmacy-value {
            color: #666;
            flex: 1;
          }
        }

        .patient-pharmacy {
          margin-top: 0.25rem;
        }
      }
    }

    .card-actions {
      .menu-container {
        position: relative;

        .menu-toggle {
          background: transparent;
          border: none;
          padding: 0.5rem;
          cursor: pointer;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &:hover {
            background: #f0f0f0;
          }

          .ellipsis-icon {
            width: 18px;
            height: 18px;
            color: #666;
            transform: rotate(90deg);
          }
        }

        .action-menu {
          position: absolute;
          top: 100%;
          right: 0;
          background: white;
          border: 1px solid #C7DAEC;
          border-radius: 6px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          z-index: 1000;
          min-width: 140px;
          overflow: hidden;

          .menu-item {
            display: block;
            width: 100%;
            padding: 0.75rem 1rem;
            background: none;
            border: none;
            text-align: left;
            font-size: 0.875rem;
            color: #333;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
              background-color: #f8f9fa;
            }

            &:not(:last-child) {
              border-bottom: 1px solid #f0f0f0;
            }
          }
        }
      }
    }
  }

  .card-separator {
    position: relative;
    width: 100%;
    height: 0;
    border: 1px solid #C7DAEC;
    margin: 1rem 0;
    opacity: 1;
  }

  .card-content {
    .details-grid {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .detail-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 1rem;

        .detail-label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #728A9B;
          flex-shrink: 0;
          min-width: fit-content;
        }

        .detail-value {
          font-size: 0.875rem;
          color: #333;
          font-weight: 500;
          flex: 1;
        }

        .badge {
          padding: 0.375rem 0.75rem;
          border-radius: 20px;
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: uppercase;

          &.badge-priority {
            background-color: #FFEBEE;
            color: #C62828;
          }

          &.badge-cancelled {
            background-color: #FFCDD2;
            color: #D32F2F;
          }

          &.badge-in-progress {
            background-color: #FFF3E0;
            color: #F57C00;
          }

          // New prescription status badges
          &.badge-pending {
            background-color: #FFF3E0;
            color: #F57C00;
          }

          &.badge-approved {
            background-color: #E8F5E8;
            color: #2E7D32;
          }

          &.badge-rejected {
            background-color: #FFCDD2;
            color: #D32F2F;
          }

          &.badge-expired {
            background-color: #F5F5F5;
            color: #757575;
          }

          &.badge-uploaded {
            background-color: #E8F5E8;
            color: #2E7D32;
          }

          &.badge-default {
            background-color: #E3F2FD;
            color: #1976D2;
          }

          // Patient status badges
          &.badge-active {
            background-color: #E8F5E8;
            color: #2E7D32;
          }

          &.badge-hospitalized {
            background-color: #FFF3E0;
            color: #F57C00;
          }

          &.badge-vacation {
            background-color: #E3F2FD;
            color: #1976D2;
          }

          // Pharmacy status badges
          &.badge-open {
            background-color: #E8F5E8;
            color: #2E7D32;
          }

          &.badge-closed {
            background-color: #FFCDD2;
            color: #D32F2F;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .prescriptions-container {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;

    .header-right {
      justify-content: space-between;

      .date-range-selector {
        flex: 1;
        min-width: 0;

        .date-items {
          overflow-x: auto;
          scrollbar-width: none;
          -ms-overflow-style: none;

          &::-webkit-scrollbar {
            display: none;
          }
        }
      }
    }
  }

  .table-container {
    overflow-x: auto;

    .prescriptions-table {
      min-width: 800px;
    }
  }

  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;

    .prescription-card {
      padding: 1rem;

      .card-header {
        .patient-info {
          .initials-badge {
            width: 40px;
            height: 40px;
            font-size: 0.75rem;
          }

          .patient-details {
            .patient-name {
              font-size: 0.875rem;
            }

            .patient-foyer {
              font-size: 0.75rem;

              .foyer-label {
                font-size: 0.75rem;
              }

              .foyer-value {
                font-size: 0.75rem;
              }
            }
          }
        }
      }

      .card-content {
        .details-grid {
          gap: 0.75rem;

          .detail-item {
            .detail-label {
              font-size: 0.75rem;
            }

            .detail-value {
              font-size: 0.75rem;
            }

            .badge {
              font-size: 0.6875rem;
              padding: 0.25rem 0.5rem;
            }
          }
        }
      }
    }
  }
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  margin-top: 20px;
  border-top: 1px solid #E8EDF5;
  gap: 20px;
  flex-wrap: wrap;
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-label {
  font-size: 14px;
  color: #163659;
  font-weight: 500;
}

.page-size-selector {
  padding: 6px 12px;
  border: 1px solid #C7DAEC;
  border-radius: 6px;
  background: white;
  color: #163659;
  font-size: 14px;
  cursor: pointer;
  min-width: 60px;
}

.page-size-selector:focus {
  outline: none;
  border-color: #1061AC;
  box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
}

.pagination-info {
  flex: 1;
  text-align: center;
}

.pagination-text {
  font-size: 14px;
  color: #728A9B;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid #C7DAEC;
  background: white;
  color: #163659;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
  background: #F5F7FA;
  border-color: #1061AC;
  color: #1061AC;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #F8F9FA;
}

.pagination-btn.page-number.active {
  background: #1061AC;
  border-color: #1061AC;
  color: white;
}

.pagination-btn.page-number.active:hover {
  background: #0D4E8C;
  border-color: #0D4E8C;
}

.pagination-btn svg {
  width: 16px;
  height: 16px;
}

/* Responsive pagination */
@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .pagination-info {
    text-align: center;
    order: -1;
  }

  .items-per-page {
    justify-content: center;
  }

  .pagination-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
}

// Loading spinner styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  min-height: 200px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1061AC;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  p {
    color: #728A9B;
    font-size: 0.9rem;
    margin: 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
