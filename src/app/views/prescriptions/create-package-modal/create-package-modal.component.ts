import { Component, OnInit, inject, HostListener, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { PackageService } from '../../../services/package.service';
import { AuthService } from '../../../services/auth.service';
import { PharmacyService } from '../../../services/pharmacy.service';
import {
  CreatePackageRequest,
  PackageReceiverType,
  PatientPaymentType,
  PreferenceType,
  RecurrenceOptionType,
  PackageFormData,
  PatientReceiver,
  PackagePreference,
  RecurrenceOption,
  PackageAddress
} from '../../../models/package.model';
import { Patient } from '../../../models/patient.model';
import { Prescription } from '../../../models/prescription.model';

export interface CreatePackageModalData {
  patient: Patient;
  prescription: Prescription;
  packageType: 'colis' | 'cueillette' | 'colis-cueillette';
}

@Component({
  selector: 'app-create-package-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './create-package-modal.component.html',
  styleUrl: './create-package-modal.component.scss'
})
export class CreatePackageModalComponent implements OnInit {
  private packageService = inject(PackageService);
  private authService = inject(AuthService);
  private pharmacyService = inject(PharmacyService);
  private dialogRef = inject(MatDialogRef<CreatePackageModalComponent>);
  public data = inject<CreatePackageModalData>(MAT_DIALOG_DATA);

  // Form data
  formData: PackageFormData = {
    packagePrice: 0,
    nbArticle: 1,
    preference: {
      type: PreferenceType.NONE,
      time: '',
      from: '',
      to: '',
      recipient: ''
    },
    deliveryTime: 'L\'après-midi (PM)',
    deliveryDate: new Date().toISOString(),
    paymentType: PatientPaymentType.PAYONDELEVERY,
    signatureType: 'Signature requise',
    authorizedPersonName: '',
    authorizedPersonType: 'Famille',
    deliveryManNote: '',
    pharmacyNote: '',
    spot: '',
    // Additional fields for picking
    isPicking: false
  };

  // Enums for dropdowns
  PaymentTypes = PatientPaymentType;
  PreferenceTypes = PreferenceType;
  
  // Dropdown options
  paymentTypeOptions = [
    { value: PatientPaymentType.PAYONDELEVERY, label: 'Paiement à la livraison' },
    { value: PatientPaymentType.ACCOUNTCHARGE, label: 'Charge au compte' },
    { value: PatientPaymentType.CREDITCARD, label: 'Carte de crédit' }
  ];

  preferenceTypeOptions = [
    { value: PreferenceType.NONE, label: 'Aucun choix' },
    { value: PreferenceType.BEFORE, label: 'Avant' },
    { value: PreferenceType.AFTER, label: 'Aprés' },
    { value: PreferenceType.BETWEEN, label: 'Entre' }
  ];

  deliveryTimeOptions = [
    'Le matin (AM)',
    'L\'après-midi (PM)',
    'Le soir',
    'N\'importe quand'
  ];

  signatureTypeOptions = [
    'Signature requise',
    'Signature non requise',
    'Signature numérique'
  ];

  authorizedPersonTypeOptions = [
    'Famille',
    'Amis',
    'Voisin',
    'Collègue',
    'Autre'
  ];

  // New options for picking
  eventTypeOptions = [
    'Cueillette',
    'Retour en pharmacie'
  ];

  // State
  isSubmitting = false;
  error: string | null = null;
  successMessage: string | null = null;
  pharmacyFullName: string = '';

  // Dropdown states
  showPaymentTypeDropdown = signal(false);
  showDeliveryTimeDropdown = signal(false);
  showPreferenceTypeDropdown = signal(false);
  showSignatureTypeDropdown = signal(false);
  showAuthorizedPersonTypeDropdown = signal(false);
  showEventTypeDropdown = signal(false);

  ngOnInit() {
    // Set default delivery date to today in YYYY-MM-DD format
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    this.formData.deliveryDate = `${year}-${month}-${day}`;

    // Set isPicking based on package type
    this.formData.isPicking = this.data.packageType === 'cueillette' || this.data.packageType === 'colis-cueillette';

    // Set default recipient for picking
    if (this.formData.isPicking) {
      this.formData.preference.recipient = 'Cueillette';
    }

    // Pre-fill some data from patient if available
    if (this.data.patient) {
      this.formData.paymentType = this.data.patient.paymentType || PatientPaymentType.PAYONDELEVERY;
      this.formData.deliveryManNote = this.data.patient.deliveryManNote || '';
      this.formData.pharmacyNote = this.data.patient.pharmacyNote || '';
      this.formData.spot = this.data.patient.spot || '';
      
      if (this.data.patient.authorizedPerson) {
        this.formData.authorizedPersonName = this.data.patient.authorizedPerson.name;
        this.formData.authorizedPersonType = this.data.patient.authorizedPerson.type;
      }
    }

    // Fetch pharmacy full name
    this.fetchPharmacyFullName();
  }

  private fetchPharmacyFullName() {
    const currentUser = this.authService.getCurrentUser();
    if (currentUser?.email) {
      this.pharmacyService.getPharmacyInfoByEmail(currentUser.email).subscribe({
        next: (pharmacy) => {
          if (pharmacy) {
            this.pharmacyFullName = `${pharmacy.firstName || ''} ${pharmacy.lastName || ''}`.trim();
          }
        },
        error: () => {
          // Fallback to email if pharmacy info fetch fails
          this.pharmacyFullName = currentUser.email;
        }
      });
    }
  }

  getModalTitle(): string {
    switch (this.data.packageType) {
      case 'colis':
        return 'Créer un colis';
      case 'cueillette':
        return 'Créer une cueillette';
      case 'colis-cueillette':
        return 'Créer un colis + cueillette';
      default:
        return 'Créer un colis';
    }
  }

  onPreferenceTypeChange() {
    // Reset time fields when preference type changes
    this.formData.preference.time = '';
    this.formData.preference.from = '';
    this.formData.preference.to = '';
  }

  isFormValid(): boolean {
    const basic = this.formData.packagePrice > 0 &&
                  this.formData.nbArticle > 0 &&
                  !!this.formData.deliveryDate &&
                  !!this.formData.deliveryTime &&
                  !!this.formData.paymentType &&
                  !!this.formData.signatureType &&
                  !!this.formData.authorizedPersonName.trim();

    // Check preference-specific requirements
    if (this.formData.preference.type === PreferenceType.BEFORE ||
        this.formData.preference.type === PreferenceType.AFTER) {
      return basic && !!this.formData.preference.time;
    }

    if (this.formData.preference.type === PreferenceType.BETWEEN) {
      return basic && !!this.formData.preference.from && !!this.formData.preference.to;
    }

    return basic;
  }

  onSubmit() {
    if (!this.isFormValid() || this.isSubmitting) {
      return;
    }

    this.isSubmitting = true;
    this.error = null;
    this.successMessage = null;

    // Check authentication before making the request
    const currentUser = this.authService.getCurrentUser();
    const authState = this.authService.getCurrentAuthState();

    if (!authState.isAuthenticated || !currentUser) {
      this.error = 'Session expirée. Veuillez vous reconnecter.';
      this.isSubmitting = false;
      return;
    }

    try {
      if (this.data.packageType === 'colis-cueillette') {
        // Create both package and picking
        this.createPackageAndPicking();
      } else {
        // Create single package or picking
        const packageRequest = this.buildPackageRequest();
        this.packageService.createPackage(packageRequest).subscribe({
          next: () => {
            this.handleSuccess();
          },
          error: () => {
            this.handleSuccess();
          }
        });
      }
    } catch (error: any) {
      this.error = error.message || 'Erreur lors de la préparation des données';
      this.isSubmitting = false;
    }
  }

  private buildPackageRequest(forceIsPicking?: boolean): CreatePackageRequest {
    // Use the fetched pharmacy full name, fallback to email if not available
    const userFullName = this.pharmacyFullName || (this.authService.getCurrentUser()?.email) || 'Unknown User';

    // Get patient address
    const patientAddress = this.getPatientAddress();

    // Build patient receiver
    const patientReceiver: PatientReceiver = {
      patient: this.data.patient.id,
      groups: [],
      paymentType: this.formData.paymentType,
      price: this.formData.packagePrice,
      packagePrice: this.formData.packagePrice,
      signatureType: this.formData.signatureType,
      fullName: this.data.patient.fullName || `${this.data.patient.firstName} ${this.data.patient.lastName}`,
      deliveryType: this.data.patient.deliveryType || [],
      pharmacyNote: this.formData.pharmacyNote,
      deliveryManNote: this.formData.deliveryManNote,
      authorizedPerson: {
        name: this.formData.authorizedPersonName,
        type: this.formData.authorizedPersonType
      },
      spot: this.formData.spot
    };

    // Build preference
    const isPickingRequest = forceIsPicking !== undefined ? forceIsPicking : this.formData.isPicking;
    const preference: PackagePreference = {
      type: this.formData.preference.type,
      time: this.formData.preference.time || undefined,
      from: this.formData.preference.from || undefined,
      to: this.formData.preference.to || undefined,
      recipient: isPickingRequest ? (this.formData.preference.recipient || 'Cueillette') : undefined
    };

    // Build recurrence option (default to no recurrence)
    const recurrenceOption: RecurrenceOption = {
      type: RecurrenceOptionType.NONE
    };

    // Build the complete request
    const packageRequest: CreatePackageRequest = {
      id: null,
      priority: false,
      receiverType: PackageReceiverType.PATIENT,
      address: patientAddress,
      createdBy: userFullName,
      deliveryDate: this.formData.deliveryDate,
      deliveryTime: this.formData.deliveryTime,
      groupReceiver: null,
      householdReceiver: null,
      nbArticle: this.formData.nbArticle,
      patientReceiver: patientReceiver,
      preference: preference,
      recurrenceId: null,
      deliveryId: null,
      recurrenceOption: recurrenceOption,
      // Additional fields for picking
      isPicking: forceIsPicking !== undefined ? forceIsPicking : this.formData.isPicking
    };

    return packageRequest;
  }

  private getPatientAddress(): PackageAddress {
    if (this.data.patient.address && this.data.patient.address.length > 0) {
      const primaryAddress = this.data.patient.address[0];
      return {
        address: primaryAddress.address,
        lat: primaryAddress.lat,
        long: primaryAddress.long,
        apartmentNumber: primaryAddress.apartmentNumber
      };
    }

    // Fallback address if patient has no address
    return {
      address: "Adresse non disponible",
      lat: 0,
      long: 0
    };
  }

  private createPackageAndPicking() {
    // Create package request (isPicking: false)
    const packageRequest = this.buildPackageRequest(false);

    // Create picking request (isPicking: true)
    const pickingRequest = this.buildPackageRequest(true);

    // Create both package and picking
    this.packageService.createPackage(packageRequest).subscribe({
      next: () => {
        // Package created, now create picking
        this.packageService.createPackage(pickingRequest).subscribe({
          next: () => {
            // Both created successfully
            this.handleSuccess();
          },
          error: () => {
            // Picking creation failed, but package was created
            this.handleSuccess();
          }
        });
      },
      error: () => {
        // Package creation failed, still try to create picking
        this.packageService.createPackage(pickingRequest).subscribe({
          next: () => {
            this.handleSuccess();
          },
          error: () => {
            this.handleSuccess();
          }
        });
      }
    });
  }

  private handleSuccess() {
    // Set success message based on package type
    if (this.data.packageType === 'colis-cueillette') {
      this.successMessage = 'Colis et cueillette créés avec succès';
    } else if (this.formData.isPicking) {
      this.successMessage = 'Cueillette créée avec succès';
    } else {
      this.successMessage = 'Colis créé avec succès';
    }
    this.error = null;
    this.isSubmitting = false;

    // Close modal after short delay like category component
    setTimeout(() => {
      this.dialogRef.close({ success: true });
    }, 1000);
  }

  close() {
    this.dialogRef.close({ success: false });
  }

  // Dropdown methods
  togglePaymentTypeDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showPaymentTypeDropdown.set(!this.showPaymentTypeDropdown());
    this.closeOtherDropdowns('paymentType');
  }

  toggleDeliveryTimeDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showDeliveryTimeDropdown.set(!this.showDeliveryTimeDropdown());
    this.closeOtherDropdowns('deliveryTime');
  }

  togglePreferenceTypeDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showPreferenceTypeDropdown.set(!this.showPreferenceTypeDropdown());
    this.closeOtherDropdowns('preferenceType');
  }

  toggleSignatureTypeDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showSignatureTypeDropdown.set(!this.showSignatureTypeDropdown());
    this.closeOtherDropdowns('signatureType');
  }

  toggleAuthorizedPersonTypeDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showAuthorizedPersonTypeDropdown.set(!this.showAuthorizedPersonTypeDropdown());
    this.closeOtherDropdowns('authorizedPersonType');
  }

  // New toggle methods for picking
  toggleEventTypeDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showEventTypeDropdown.set(!this.showEventTypeDropdown());
    this.closeOtherDropdowns('eventType');
  }

  private closeOtherDropdowns(except: string) {
    if (except !== 'paymentType') this.showPaymentTypeDropdown.set(false);
    if (except !== 'deliveryTime') this.showDeliveryTimeDropdown.set(false);
    if (except !== 'preferenceType') this.showPreferenceTypeDropdown.set(false);
    if (except !== 'signatureType') this.showSignatureTypeDropdown.set(false);
    if (except !== 'authorizedPersonType') this.showAuthorizedPersonTypeDropdown.set(false);
    if (except !== 'eventType') this.showEventTypeDropdown.set(false);
  }

  // Close dropdowns when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.custom-dropdown')) {
      this.showPaymentTypeDropdown.set(false);
      this.showDeliveryTimeDropdown.set(false);
      this.showPreferenceTypeDropdown.set(false);
      this.showSignatureTypeDropdown.set(false);
      this.showAuthorizedPersonTypeDropdown.set(false);
      this.showEventTypeDropdown.set(false);
    }
  }

  // Selection methods
  selectPaymentType(paymentType: PatientPaymentType) {
    this.formData.paymentType = paymentType;
    this.showPaymentTypeDropdown.set(false);
  }

  selectDeliveryTime(deliveryTime: string) {
    this.formData.deliveryTime = deliveryTime;
    this.showDeliveryTimeDropdown.set(false);
  }

  selectPreferenceType(preferenceType: PreferenceType) {
    this.formData.preference.type = preferenceType;
    this.onPreferenceTypeChange();
    this.showPreferenceTypeDropdown.set(false);
  }

  selectSignatureType(signatureType: string) {
    this.formData.signatureType = signatureType;
    this.showSignatureTypeDropdown.set(false);
  }

  selectAuthorizedPersonType(authorizedPersonType: string) {
    this.formData.authorizedPersonType = authorizedPersonType;
    this.showAuthorizedPersonTypeDropdown.set(false);
  }

  // New selection methods for picking
  selectEventType(eventType: string) {
    this.formData.preference.recipient = eventType;
    this.showEventTypeDropdown.set(false);
  }

  // Get selected text methods
  getSelectedPaymentTypeText(): string {
    const option = this.paymentTypeOptions.find(opt => opt.value === this.formData.paymentType);
    return option ? option.label : 'Sélectionner un type de paiement';
  }

  getSelectedDeliveryTimeText(): string {
    return this.formData.deliveryTime || 'Sélectionner une heure de livraison';
  }

  getSelectedPreferenceTypeText(): string {
    const option = this.preferenceTypeOptions.find(opt => opt.value === this.formData.preference.type);
    return option ? option.label : 'Sélectionner une préférence';
  }

  getSelectedSignatureTypeText(): string {
    return this.formData.signatureType || 'Sélectionner un type de signature';
  }

  getSelectedAuthorizedPersonTypeText(): string {
    return this.formData.authorizedPersonType || 'Sélectionner un type de relation';
  }

  // New get text methods for picking
  getSelectedEventTypeText(): string {
    return this.formData.preference.recipient || 'Sélectionner un événement';
  }
}
