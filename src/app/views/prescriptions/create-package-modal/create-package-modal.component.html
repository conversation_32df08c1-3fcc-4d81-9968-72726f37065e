<div class="modal-container">
  <!-- <PERSON><PERSON> -->
  <div class="modal-header">
    <h2 class="modal-title">{{ getModalTitle() }}</h2>
    <button class="close-button" (click)="close()" [disabled]="isSubmitting">
      <span>&times;</span>
    </button>
  </div>

  <!-- Error Message -->
  @if (error) {
    <div class="error-message">
      <span>{{ error }}</span>
    </div>
  }

  <!-- Success Message -->
  @if (successMessage) {
    <div class="success-message">
      <span>{{ successMessage }}</span>
    </div>
  }

  <!-- Modal Content -->
  <div class="modal-content">
    @if (data && data.patient) {
      <!-- Patient Info Section -->
      <div class="patient-info-section">
        <h3 class="section-title">Patient</h3>
        <div class="patient-info">
          <div class="patient-avatar">
            <span class="initials">{{ data.patient.initials || ((data.patient.firstName?.charAt(0) || '') + (data.patient.lastName?.charAt(0) || '')) }}</span>
          </div>
          <div class="patient-details">
            <div class="patient-name">{{ data.patient.fullName || (data.patient.firstName + ' ' + data.patient.lastName) }}</div>
            <div class="patient-address">{{ data.patient.primaryAddress || 'Adresse non disponible' }}</div>
          </div>
        </div>
      </div>
    } @else {
      <div class="error-message">
        <span>Données patient non disponibles</span>
      </div>
    }

    <!-- Form Section -->
    <div class="form-section">
      <!-- Row 1: Package Price and Payment Type -->
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">Prix du colis *</label>
          <input
            type="number"
            class="form-input"
            [(ngModel)]="formData.packagePrice"
            placeholder="0.00"
            min="0"
            step="0.01"
            [disabled]="isSubmitting"
          >
        </div>
        <div class="form-group">
          <label class="form-label">Nombre d'articles *</label>
          <input
            type="number"
            class="form-input"
            [(ngModel)]="formData.nbArticle"
            placeholder="Entrez le nombre d'articles"
            min="1"
            step="1"
            [disabled]="isSubmitting"
          >
        </div>
        <div class="form-group">
          <label class="form-label">Type de paiement *</label>
          <div class="custom-dropdown">
            <button type="button" class="dropdown-toggle" (click)="togglePaymentTypeDropdown($event)" [disabled]="isSubmitting">
              <span>{{ getSelectedPaymentTypeText() }}</span>
            </button>
            @if (showPaymentTypeDropdown()) {
              <div class="dropdown-menu">
                @for (option of paymentTypeOptions; track option.value) {
                  <div class="dropdown-item" (click)="selectPaymentType(option.value)" [class.selected]="formData.paymentType === option.value">
                    <span class="option-text">{{ option.label }}</span>
                  </div>
                }
              </div>
            }
          </div>
        </div>
      </div>

      <!-- Row 2: Delivery Date and Time -->
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">Date de livraison *</label>
          <input
            type="date"
            class="form-input"
            [(ngModel)]="formData.deliveryDate"
            [disabled]="isSubmitting"
          >
        </div>
        <div class="form-group">
          <label class="form-label">Heure de livraison *</label>
          <div class="custom-dropdown">
            <button type="button" class="dropdown-toggle" (click)="toggleDeliveryTimeDropdown($event)" [disabled]="isSubmitting">
              <span>{{ getSelectedDeliveryTimeText() }}</span>
            </button>
            @if (showDeliveryTimeDropdown()) {
              <div class="dropdown-menu">
                @for (option of deliveryTimeOptions; track option) {
                  <div class="dropdown-item" (click)="selectDeliveryTime(option)" [class.selected]="formData.deliveryTime === option">
                    <span class="option-text">{{ option }}</span>
                  </div>
                }
              </div>
            }
          </div>
        </div>
      </div>

      <!-- Row 3: Preference Type and Signature Type -->
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">Préférence de livraison *</label>
          <div class="custom-dropdown">
            <button type="button" class="dropdown-toggle" (click)="togglePreferenceTypeDropdown($event)" [disabled]="isSubmitting">
              <span>{{ getSelectedPreferenceTypeText() }}</span>
            </button>
            @if (showPreferenceTypeDropdown()) {
              <div class="dropdown-menu">
                @for (option of preferenceTypeOptions; track option.value) {
                  <div class="dropdown-item" (click)="selectPreferenceType(option.value)" [class.selected]="formData.preference.type === option.value">
                    <span class="option-text">{{ option.label }}</span>
                  </div>
                }
              </div>
            }
          </div>
        </div>
        <div class="form-group">
          <label class="form-label">Type de signature *</label>
          <div class="custom-dropdown">
            <button type="button" class="dropdown-toggle" (click)="toggleSignatureTypeDropdown($event)" [disabled]="isSubmitting">
              <span>{{ getSelectedSignatureTypeText() }}</span>
            </button>
            @if (showSignatureTypeDropdown()) {
              <div class="dropdown-menu">
                @for (option of signatureTypeOptions; track option) {
                  <div class="dropdown-item" (click)="selectSignatureType(option)" [class.selected]="formData.signatureType === option">
                    <span class="option-text">{{ option }}</span>
                  </div>
                }
              </div>
            }
          </div>
        </div>
      </div>

      <!-- Conditional Preference Fields -->
      @if (formData.preference.type === PreferenceTypes.BEFORE || formData.preference.type === PreferenceTypes.AFTER) {
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">Heure *</label>
            <input
              type="time"
              class="form-input"
              [(ngModel)]="formData.preference.time"
              [disabled]="isSubmitting"
              placeholder="HH:MM"
            >
          </div>
        </div>
      }

      @if (formData.preference.type === PreferenceTypes.BETWEEN) {
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">De *</label>
            <input
              type="time"
              class="form-input"
              [(ngModel)]="formData.preference.from"
              [disabled]="isSubmitting"
              placeholder="HH:MM"
            >
          </div>
          <div class="form-group">
            <label class="form-label">À *</label>
            <input
              type="time"
              class="form-input"
              [(ngModel)]="formData.preference.to"
              [disabled]="isSubmitting"
              placeholder="HH:MM"
            >
          </div>
        </div>
      }

      <!-- Picking-specific fields (show for cueillette and colis-cueillette) -->
      @if (formData.isPicking || data.packageType === 'colis-cueillette') {
        <!-- Row 4.1: Event Type (Événement) -->
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">Événement *</label>
            <div class="custom-dropdown">
              <button type="button" class="dropdown-toggle" (click)="toggleEventTypeDropdown($event)" [disabled]="isSubmitting">
                <span>{{ getSelectedEventTypeText() }}</span>
              </button>
              @if (showEventTypeDropdown()) {
                <div class="dropdown-menu">
                  @for (option of eventTypeOptions; track option) {
                    <div class="dropdown-item" (click)="selectEventType(option)" [class.selected]="formData.preference.recipient === option">
                      <span class="option-text">{{ option }}</span>
                    </div>
                  }
                </div>
              }
            </div>
          </div>
          <div class="form-group">
            <!-- Empty space to make Événement dropdown half-width -->
          </div>
        </div>
      }

      <!-- Row 5: Authorized Person -->
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">Personne autorisée *</label>
          <input
            type="text"
            class="form-input"
            [(ngModel)]="formData.authorizedPersonName"
            placeholder="Nom de la personne autorisée"
            [disabled]="isSubmitting"
          >
        </div>
        <div class="form-group">
          <label class="form-label">Type de relation *</label>
          <div class="custom-dropdown">
            <button type="button" class="dropdown-toggle" (click)="toggleAuthorizedPersonTypeDropdown($event)" [disabled]="isSubmitting">
              <span>{{ getSelectedAuthorizedPersonTypeText() }}</span>
            </button>
            @if (showAuthorizedPersonTypeDropdown()) {
              <div class="dropdown-menu">
                @for (option of authorizedPersonTypeOptions; track option) {
                  <div class="dropdown-item" (click)="selectAuthorizedPersonType(option)" [class.selected]="formData.authorizedPersonType === option">
                    <span class="option-text">{{ option }}</span>
                  </div>
                }
              </div>
            }
          </div>
        </div>
      </div>

      <!-- Row 6: Notes -->
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">Note pharmacie</label>
          <textarea
            class="form-textarea"
            [(ngModel)]="formData.pharmacyNote"
            placeholder="Note pour la pharmacie"
            rows="2"
            [disabled]="isSubmitting"
          ></textarea>
        </div>
        <div class="form-group">
          <label class="form-label">Note livreur</label>
          <textarea
            class="form-textarea"
            [(ngModel)]="formData.deliveryManNote"
            placeholder="Note pour le livreur"
            rows="2"
            [disabled]="isSubmitting"
          ></textarea>
        </div>
      </div>

      <!-- Row 7: Spot -->
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">Endroit désigné
</label>
          <input
            type="text"
            class="form-input"
            [(ngModel)]="formData.spot"
            placeholder="Lieu spécifique de livraison"
            [disabled]="isSubmitting"
          >
        </div>
      </div>
    </div>
  </div>

  <!-- Modal Footer -->
  <div class="modal-footer">
    <button class="cancel-button" (click)="close()" [disabled]="isSubmitting">
      Annuler
    </button>
    <button
      class="submit-button"
      (click)="onSubmit()"
      [disabled]="!isFormValid() || isSubmitting"
      [class.loading]="isSubmitting"
    >
      @if (isSubmitting) {
        <span class="loading-spinner"></span>
        Création en cours...
      } @else {
        Créer le colis
      }
    </button>
  </div>
</div>
