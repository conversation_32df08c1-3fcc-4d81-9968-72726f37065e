<div class="prescription-details-container">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-top">
      <div class="header-navigation">
        <button class="back-button" (click)="goBack()">
          <span class="back-arrow">&lt;</span>
          <span>Liste des ordonnances</span>
        </button>
      </div>
      <!-- Action Buttons -->
      <div class="action-buttons">
        <button class="action-btn primary" (click)="creerColis()">Créer un colis</button>
        <button class="action-btn primary" (click)="creerCueillette()">Créer une cueillette</button>
        <button class="action-btn primary" (click)="creerColisCueillette()">Créer un colis + cueillette</button>
      </div>
    </div>
    <h1 class="page-title">Détails de l'ordonnance</h1>
  </div>

  <!-- Loading State -->
  @if (loading) {
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Chargement des détails de l'ordonnance...</p>
    </div>
  }

  <!-- Error State -->
  @if (error) {
    <div class="error-container">
      <p class="error-message">{{ error }}</p>
      <button class="retry-btn" (click)="loadPrescriptionData()">Réessayer</button>
    </div>
  }

  @if (!loading && !error && prescription) {
    <!-- Two Column Layout -->
    <div class="panels-container">
      <!-- Left Column: Information + Patient -->
      <div class="left-column">
        <!-- Panel 1: Information de l'ordonnance -->
        <div class="panel info-panel">
          <div class="panel-header">
            <h2 class="panel-title">Information de l'ordonnance</h2>
          </div>

          <div class="fields-grid">
            <div class="fields-row">
              <div class="field-item">
                <span class="field-label">Numéro :</span>
                <span class="field-value">{{ patient?.phone || 'Chargement...' }}</span>
              </div>

              <div class="field-item">
                <span class="field-label">Nom du Patient :</span>
                <span class="field-value">{{ patient?.fullName || 'Chargement...' }}</span>
              </div>

              <div class="field-item">
                <span class="field-label">Nom du Foyer :</span>
                <span class="field-value">{{ householdName || '----------' }}</span>
              </div>

              <div class="field-item">
                <span class="field-label">Date d'envoi :</span>
                <span class="field-value">{{ prescription.issueDate | date:'dd/MM/yyyy' }}</span>
              </div>
            </div>
          </div>

          <div class="badges-section">
            <div class="badge-item">
              <span class="badge-label">Statut d'ordonnances :</span>
              <span class="badge" [ngClass]="getBadgeClass(prescription.prescriptionStatus)">
                {{ getBadgeText(prescription.prescriptionStatus) }}
              </span>
            </div>
          </div>

          <!-- Note Section -->
          <div class="note-section">
            <div class="note-item">
              <span class="note-label">Note :</span>
              <span class="note-value">{{ prescription.note || 'Aucune note disponible' }}</span>
            </div>
          </div>
        </div>

        <!-- Panel 2: Informations du patient -->
        <div class="panel patient-panel">
          <div class="panel-header">
            <h2 class="panel-title">Informations du patient</h2>
            <button class="panel-action-btn" title="Modifier">
              <img src="assets/images/edit.svg" alt="Edit" class="action-icon" />
            </button>
          </div>

          <div class="patient-header">
            <div class="patient-avatar">{{ patient?.initials || 'PA' }}</div>
            <div class="patient-name">{{ patient?.fullName || 'Chargement...' }}</div>
          </div>

          <div class="divider"></div>

          <div class="patient-details">
            <div class="details-row">
              <div class="detail-column">
                <span class="detail-label">E-mail :</span>
                <span class="detail-value">{{ patient?.email || 'Non spécifié' }}</span>
              </div>

              <div class="detail-column">
                <span class="detail-label">N° de téléphone :</span>
                <span class="detail-value">{{ patient?.phone || 'Non spécifié' }}</span>
              </div>

              <div class="detail-column">
                <span class="detail-label">Adresse :</span>
                <div class="detail-value">
                  @if (patient?.primaryAddress) {
                    <span>{{ patient?.primaryAddress }}</span>
                  } @else if (patient && patient.address && patient.address.length > 0) {
                    @for (addressItem of patient.address; track $index) {
                      <div>{{ addressItem.address }}</div>
                      @if (addressItem.apartmentNumber) {
                        <div>Appartement: {{ addressItem.apartmentNumber }}</div>
                      }
                    }
                  } @else {
                    <span>Non spécifié</span>
                  }
                </div>
              </div>
            </div>

            <div class="footer-row">
              <div class="footer-item">
                <span class="footer-label">Foyer :</span>
                <span class="footer-value">{{ householdName || '----------' }}</span>
              </div>

              <div class="footer-item">
                <span class="footer-label">Patient hospitalisé :</span>
                <span class="footer-value">{{ patient?.hospitalizedPatient ? 'Oui' : 'Non' }}</span>
              </div>

              <div class="footer-item">
                <span class="footer-label">Patient en vacances :</span>
                <span class="footer-value">Non spécifié</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column: Ordonnances -->
      <div class="right-column">
        <div class="panel ordonnances-panel">
          <div class="panel-header">
            <h2 class="panel-title">Ordonnances</h2>
            <button class="panel-action-btn" (click)="downloadPrescription(prescription)" title="Télécharger">
              <svg cIcon name="cil-cloud-download"></svg>
            </button>
          </div>

          <div class="prescription-viewer">
            @if (prescription.storagePath && prescription.storagePath.length > 0) {
              @if (getFileType(prescription.storagePath[0]) === 'pdf') {
                <!-- PDF Viewer in Container -->
                <div class="pdf-embedded-container">
                  <div class="pdf-header">
                    <span class="pdf-title">Document PDF</span>
                    <button class="pdf-fullscreen-btn" (click)="openFileViewer()" title="Ouvrir en plein écran">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 3H5C3.89543 3 3 3.89543 3 5V8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M21 8V5C21 3.89543 20.1046 3 19 3H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M16 21H19C20.1046 21 21 20.1046 21 19V16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M3 16V19C3 20.1046 3.89543 21 5 21H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </button>
                  </div>
                  <div class="pdf-content-wrapper">
                    @if (pdfLoading) {
                      <div class="pdf-loading">
                        <div class="loading-spinner"></div>
                        <p>Chargement du PDF...</p>
                      </div>
                    }



                    <!-- Main PDF Preview -->
                    @if (pdfLoading) {
                      <div class="pdf-loading-preview">
                        <div class="loading-spinner"></div>
                        <p>Chargement du PDF...</p>
                      </div>
                    }

                    @if (pdfError) {
                      <div class="pdf-error-preview">
                        <p>{{ pdfError }}</p>
                        <button class="retry-btn" (click)="loadPdfSmart(prescription.storagePath[0])">
                          Réessayer
                        </button>
                      </div>
                    }

                    @if (pdfSrc && !pdfLoading && !pdfError) {
                      <div class="pdf-preview-container">
                        <pdf-viewer
                          [src]="pdfSrc"
                          [render-text]="true"
                          [original-size]="false"
                          [fit-to-page]="false"
                          [show-all]="true"
                          [zoom]="1"
                          [zoom-scale]="'page-width'"
                          [page]="1"
                          style="display: block; width: 100%; height: 500px; border: 1px solid #C7DAEC; border-radius: 8px; cursor: pointer; overflow: auto;"
                          (after-load-complete)="onPdfLoaded()"
                          (error)="onPdfError($event)"
                          (click)="openFileViewer()">
                        </pdf-viewer>
                        <div class="pdf-overlay" (click)="openFileViewer()">
                          <div class="pdf-overlay-text">
                            <svg cIcon name="cil-fullscreen"></svg>
                            <span>Cliquer pour agrandir</span>
                          </div>
                        </div>
                      </div>
                    }

                    <!-- Debug info (hidden by default) -->
                    @if (false && prescription?.storagePath && prescription.storagePath.length > 0) {
                      <div class="pdf-debug-info">
                        <p><strong>PDF URL:</strong> {{ getFileUrl(prescription.storagePath[0]) }}</p>
                        <p><strong>File Path:</strong> {{ prescription.storagePath[0] }}</p>
                        <div class="debug-actions">
                          <button (click)="testUrlInNewTab()" class="test-url-btn">
                            🔗 Test URL in New Tab
                          </button>
                          <button (click)="copyUrlToClipboard()" class="copy-url-btn">
                            📋 Copy URL
                          </button>
                        </div>
                      </div>
                    }
                  </div>
                </div>
              } @else {
                <!-- Image Preview -->
                <img
                  [src]="getFileUrl(prescription.storagePath[0])"
                  alt="Prescription Document"
                  class="prescription-image"
                  (click)="openFileViewer()"
                />
              }
            } @else {
              <div class="no-image-placeholder">
                <p>Aucun document disponible</p>
              </div>
            }
          </div>
        </div>
      </div>
    </div>
  }

  <!-- Image Viewer Modal -->
  @if (isImageViewerOpen && currentFileType === 'image') {
    <div class="image-viewer-overlay" (click)="closeFileViewer()">
      <div class="image-viewer-container" (click)="$event.stopPropagation()">
        <div class="modal-controls">
          <button class="close-viewer" (click)="closeFileViewer()"></button>
          <button class="download-viewer" (click)="downloadPrescriptionImage()" title="Télécharger">
            <svg cIcon name="cil-cloud-download"></svg>
          </button>
        </div>

        <div class="modal-image-container">
          @if (currentFileUrl) {
            <img
              [src]="currentFileUrl"
              alt="Prescription Document Full Size"
              class="full-size-image"
            />
          }
        </div>
      </div>
    </div>
  }

  <!-- PDF Viewer Modal -->
  @if (isPdfViewerOpen && currentFileType === 'pdf') {
    <div class="image-viewer-overlay" (click)="closeFileViewer()">
      <div class="image-viewer-container" (click)="$event.stopPropagation()">
        <div class="modal-controls">
          <button class="close-viewer" (click)="closeFileViewer()"></button>
          <button class="download-viewer" (click)="downloadPrescriptionImage()" title="Télécharger">
            <svg cIcon name="cil-cloud-download"></svg>
          </button>
        </div>

        <div class="modal-pdf-container">
          @if (pdfLoading) {
            <div class="pdf-loading">
              <div class="loading-spinner"></div>
              <p>Chargement du PDF...</p>
            </div>
          }

          @if (pdfError) {
            <div class="pdf-error">
              <p>{{ pdfError }}</p>
              <p class="pdf-url-debug">URL: {{ currentFileUrl }}</p>
              <button class="retry-btn" (click)="openFileViewer()">
                Réessayer
              </button>
            </div>
          }

          @if (pdfSrc && !pdfLoading && !pdfError) {
            <pdf-viewer
              [src]="pdfSrc"
              [render-text]="true"
              [original-size]="false"
              [fit-to-page]="false"
              [show-all]="true"
              [zoom]="1"
              [zoom-scale]="'page-width'"
              [page]="1"
              style="display: block; width: 100%; height: 100%;"
              (after-load-complete)="onPdfLoaded()"
              (error)="onPdfError($event)"
              (page-rendered)="onPageRendered()">
            </pdf-viewer>
          }
        </div>
      </div>
    </div>
  }
</div>
