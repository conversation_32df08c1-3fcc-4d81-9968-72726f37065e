import { Component, OnInit, inject, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Location, CommonModule } from '@angular/common';
import { IconDirective } from '@coreui/icons-angular';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { NgxDocViewerModule } from 'ngx-doc-viewer';
import { MatDialog } from '@angular/material/dialog';
import { PrescriptionService } from '../../../services/prescription.service';
import { PatientService } from '../../../services/patient.service';
import { HouseHoldService } from '../../../services/household.service';
import { RouteEncryptionService } from '../../../services/route-encryption.service';
import { Subject, takeUntil } from 'rxjs';
import { environment } from '../../../../environments/environment';

// Import the Prescription interface from the model
import { Prescription } from '../../../models/prescription.model';
import { Patient } from '../../../models/patient.model';
import { CreatePackageModalComponent, CreatePackageModalData } from '../create-package-modal/create-package-modal.component';

@Component({
  selector: 'app-details-prescription',
  standalone: true,
  imports: [CommonModule, IconDirective, PdfViewerModule, NgxDocViewerModule, CreatePackageModalComponent],
  templateUrl: './details-prescription.component.html',
  styleUrl: './details-prescription.component.scss'
})
export class DetailsPrescriptionComponent implements OnInit, OnDestroy {
  private route = inject(ActivatedRoute);
  private location = inject(Location);
  private prescriptionService = inject(PrescriptionService);
  private patientService = inject(PatientService);
  private householdService = inject(HouseHoldService);
  private routeEncryptionService = inject(RouteEncryptionService);
  private dialog = inject(MatDialog);

  prescription: Prescription | null = null;
  patient: Patient | null = null;
  householdName: string | null = null;
  isImageViewerOpen = false;
  isPdfViewerOpen = false;
  loading = true;
  error: string | null = null;
  currentFileUrl: string | null = null;
  currentFileType: 'image' | 'pdf' | null = null;
  pdfLoading = false;
  pdfError: string | null = null;
  pdfBlobUrl: string | null = null;

  // PDF loading strategy
  pdfSrc: string | Uint8Array | null = null;
  useBlobUrl = true; // Default to blob URL for better CORS handling

  private destroy$ = new Subject<void>();

  // ID from navigation state
  prescriptionId: string | null = null;

  ngOnInit() {
    // Get encrypted prescription ID from route parameters
    const encryptedId = this.route.snapshot.paramMap.get('encryptedId');

    if (encryptedId) {
      // Decrypt the ID to get the actual prescription ID
      this.prescriptionId = this.routeEncryptionService.decryptId(encryptedId);
      console.log('Decrypted prescription ID:', this.prescriptionId);

      if (this.prescriptionId) {
        this.loadPrescriptionData();
      } else {
        this.error = 'ID de prescription invalide';
        this.loading = false;
      }
    } else {
      this.error = 'ID de prescription manquant';
      this.loading = false;
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadPrescriptionData() {
    this.loading = true;
    this.error = null;

    if (!this.prescriptionId) {
      this.error = 'ID de prescription manquant';
      this.loading = false;
      return;
    }

    // Load prescription details
    this.prescriptionService.getPrescriptionById(this.prescriptionId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (prescriptionResponse) => {
          this.prescription = prescriptionResponse;

          // Pre-load PDF if it's a PDF file
          if (prescriptionResponse.storagePath && prescriptionResponse.storagePath.length > 0) {
            const fileType = this.getFileType(prescriptionResponse.storagePath[0]);
            if (fileType === 'pdf') {
              this.loadPdfSmart(prescriptionResponse.storagePath[0]);
            }
          }

          // Get patient ID from prescription and load patient data
          if (prescriptionResponse.patientId) {
            this.loadPatientData(prescriptionResponse.patientId);
          } else {
            this.loading = false;
          }
        },
        error: () => {
          this.error = 'Erreur lors du chargement de l\'ordonnance';
          this.loading = false;
        }
      });
  }

  private loadPatientData(patientId: string) {
    this.patientService.getPatientById(patientId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (patient) => {
          this.patient = patient;

          // Load household data if patient has houseHoldId
          if (patient.houseHoldId) {
            this.loadHouseholdData(patient.houseHoldId);
          } else {
            this.householdName = null;
            this.loading = false;
          }
        },
        error: () => {
          this.error = 'Erreur lors du chargement des données patient';
          this.loading = false;
        }
      });
  }

  private loadHouseholdData(householdId: string) {
    this.householdService.getHouseHoldById(householdId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (household) => {
          this.householdName = household.displayName || household.name;
          this.loading = false;
        },
        error: () => {
          this.householdName = 'Foyer non trouvé';
          this.loading = false;
        }
      });
  }

  goBack() {
    this.location.back();
  }

  getBadgeClass(status: string): string {
    switch (status) {
      case 'UPLOADED': return 'badge-uploaded';
      case 'IN_REVIEW': return 'badge-in-review';
      case 'NEEDS_CLARIFICATION': return 'badge-needs-clarification';
      case 'APPROVED': return 'badge-approved';
      case 'REJECTED': return 'badge-rejected';
      case 'EXPIRED': return 'badge-expired';
      case 'ARCHIVED': return 'badge-archived';
      default: return 'badge-default';
    }
  }

  getBadgeText(status: string): string {
    switch (status) {
      case 'UPLOADED': return 'Téléchargée';
      case 'IN_REVIEW': return 'En révision';
      case 'NEEDS_CLARIFICATION': return 'Clarification requise';
      case 'APPROVED': return 'Approuvée';
      case 'REJECTED': return 'Rejetée';
      case 'EXPIRED': return 'Expirée';
      case 'ARCHIVED': return 'Archivée';
      default: return status;
    }
  }

  getFileUrl(filePath: string): string {
    const url = this.prescriptionService.getFileUrl(filePath);
    console.log('🔍 File path:', filePath);
    console.log('🔍 Generated URL:', url);
    return url;
  }

  // Load PDF as blob to avoid CORS issues
  async loadPdfAsBlob(filePath: string): Promise<string> {
    const blob = await new Promise<Blob>((resolve, reject) => {
      this.prescriptionService.downloadPrescriptionFile(filePath).subscribe({
        next: (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('No blob received'));
          }
        },
        error: (error) => reject(error)
      });
    });

    return URL.createObjectURL(blob);
  }

  // Load PDF as Uint8Array for ng2-pdf-viewer
  async loadPdfAsUint8Array(filePath: string): Promise<Uint8Array> {
    const blob = await new Promise<Blob>((resolve, reject) => {
      this.prescriptionService.downloadPrescriptionFile(filePath).subscribe({
        next: (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('No blob received'));
          }
        },
        error: (error) => reject(error)
      });
    });

    const arrayBuffer = await blob.arrayBuffer();
    return new Uint8Array(arrayBuffer);
  }

  // Smart PDF loading with fallback strategies
  async loadPdfSmart(filePath: string): Promise<void> {
    this.pdfLoading = true;
    this.pdfError = null;
    this.pdfSrc = null;

    try {
      if (this.useBlobUrl) {
        // Strategy 1: Try blob URL first (best for CORS)
        try {
          const blobUrl = await this.loadPdfAsBlob(filePath);
          this.pdfSrc = blobUrl;
          this.pdfBlobUrl = blobUrl;
          console.log('✅ Blob URL strategy successful');
          return;
        } catch (blobError) {
          console.log('⚠️ Blob URL strategy failed, trying Uint8Array...');
          // Strategy 2: Try Uint8Array (direct binary data)
          try {
            const uint8Array = await this.loadPdfAsUint8Array(filePath);
            this.pdfSrc = uint8Array;
            console.log('✅ Uint8Array strategy successful');
            return;
          } catch (arrayError) {
            console.log('⚠️ Uint8Array strategy failed, trying direct URL...');
          }
        }
      }

      // Strategy 3: Try proxy URL first
      try {
        const proxyUrl = this.getFileUrl(filePath);
        this.pdfSrc = proxyUrl;
        console.log('✅ Proxy URL strategy applied:', proxyUrl);
        return;
      } catch (proxyError) {
        console.log('⚠️ Proxy URL failed, trying direct backend URL...');

        // Strategy 4: Fallback to direct backend URL
        const directUrl = `${environment.apiUrl}${filePath}`;
        this.pdfSrc = encodeURI(directUrl);
        console.log('✅ Direct URL strategy applied:', directUrl);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      this.pdfError = `Impossible de charger le PDF: ${errorMessage}`;
      this.pdfSrc = null;
      console.log('❌ All PDF loading strategies failed:', error);
    } finally {
      this.pdfLoading = false;
    }
  }

  getFileType(filePath: string): 'image' | 'pdf' {
    const extension = filePath.toLowerCase().split('.').pop();
    if (extension === 'pdf') {
      return 'pdf';
    }
    return 'image'; // Default to image for jpg, png, etc.
  }

  async openFileViewer() {
    if (this.prescription?.storagePath && this.prescription.storagePath.length > 0) {
      const filePath = this.prescription.storagePath[0];
      this.currentFileUrl = this.getFileUrl(filePath);
      this.currentFileType = this.getFileType(filePath);

      if (this.currentFileType === 'pdf') {
        // Load PDF using smart loading strategy
        await this.loadPdfSmart(filePath);
        this.isPdfViewerOpen = true;
      } else {
        this.isImageViewerOpen = true;
      }
    }
  }

  closeFileViewer() {
    this.isImageViewerOpen = false;
    this.isPdfViewerOpen = false;
    this.currentFileUrl = null;
    this.currentFileType = null;

    // Clean up blob URLs to prevent memory leaks
    if (this.pdfBlobUrl && this.pdfBlobUrl.startsWith('blob:')) {
      URL.revokeObjectURL(this.pdfBlobUrl);
      this.pdfBlobUrl = null;
    }

    // Reset PDF loading state
    this.pdfLoading = false;
    this.pdfError = null;
  }

  formatDate(date: Date): string {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'short',
      year: 'numeric',
      month: 'long',
      day: '2-digit'
    });
  }

  // Legacy methods for backward compatibility
  openImageViewer() {
    this.openFileViewer();
  }

  closeImageViewer() {
    this.closeFileViewer();
  }

  downloadPrescriptionImage() {
    if (this.currentFileUrl) {
      const link = document.createElement('a');
      link.href = this.currentFileUrl;
      link.download = `prescription-${this.prescription?.id || 'document'}`;
      link.click();
    }
  }

  // PDF Event Handlers
  onPdfLoadStart() {
    this.pdfLoading = true;
    this.pdfError = null;
  }

  onPdfLoaded() {
    this.pdfLoading = false;
    this.pdfError = null;
  }

  onPdfError(error: any) {
    this.pdfLoading = false;
    this.pdfError = `Erreur lors du chargement du PDF: ${error?.message || 'Fichier PDF non accessible'}`;
  }

  onPageRendered() {
    // Page rendered successfully
  }

  // Test URL in new tab
  testUrlInNewTab() {
    if (this.prescription?.storagePath && this.prescription.storagePath.length > 0) {
      const url = this.getFileUrl(this.prescription.storagePath[0]);
      window.open(url, '_blank');
    }
  }

  // Copy URL to clipboard
  async copyUrlToClipboard() {
    if (this.prescription?.storagePath && this.prescription.storagePath.length > 0) {
      const url = this.getFileUrl(this.prescription.storagePath[0]);
      try {
        await navigator.clipboard.writeText(url);
        alert('URL copied to clipboard!');
      } catch (error) {
        // Fallback for older browsers
        try {
          const textArea = document.createElement('textarea');
          textArea.value = url;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          const successful = document.execCommand('copy');
          document.body.removeChild(textArea);
          if (successful) {
            alert('URL copied to clipboard!');
          } else {
            alert('Failed to copy URL. Please copy manually: ' + url);
          }
        } catch (fallbackError) {
          alert('Failed to copy URL. Please copy manually: ' + url);
        }
      }
    }
  }



  creerColis() {
    this.openPackageModal('colis');
  }

  creerCueillette() {
    this.openPackageModal('cueillette');
  }

  creerColisCueillette() {
    this.openPackageModal('colis-cueillette');
  }

  private openPackageModal(packageType: 'colis' | 'cueillette' | 'colis-cueillette') {
    if (!this.patient || !this.prescription) {
      console.error('Patient or prescription data not available');
      alert('Données patient ou ordonnance non disponibles');
      return;
    }

    const modalData: CreatePackageModalData = {
      patient: this.patient,
      prescription: this.prescription,
      packageType: packageType
    };

    const dialogRef = this.dialog.open(CreatePackageModalComponent, {
      data: modalData,
      width: '800px',
      maxWidth: '90vw',
      maxHeight: '85vh',
      disableClose: false,
      hasBackdrop: true,
      panelClass: 'custom-package-modal',
      position: {
        top: '10vh'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.success) {
        console.log('✅ Package created successfully:', result.package);
        alert('Colis créé avec succès!');
        // Optionally refresh data or navigate somewhere
      }
    });
  }

  downloadPrescription(prescription: Prescription) {
    console.log('Download prescription:', prescription);
    // Implement download logic here
  }


}
