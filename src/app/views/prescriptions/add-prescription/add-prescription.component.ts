import { Component, ViewEncapsulation, ViewChild, ElementRef, OnD<PERSON>roy } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PatientService } from '../../../services/patient.service';
import { HouseHoldService } from '../../../services/household.service';
import { PrescriptionService } from '../../../services/prescription.service';
import { Patient } from '../../../models/patient.model';
import { HouseHold } from '../../../models/household.model';
import { Subject, takeUntil, debounceTime, distinctUntilChanged, switchMap } from 'rxjs';

@Component({
  selector: 'app-add-prescription',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatDividerModule
  ],
  templateUrl: './add-prescription.component.html',
  styleUrls: ['./add-prescription.component.scss']
})
export class AddPrescriptionComponent implements OnDestroy {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  // File upload properties
  uploadedFile: File | null = null;
  private readonly acceptedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
  private readonly acceptedDocumentTypes = ['application/pdf'];
  private readonly maxFileSize = 10 * 1024 * 1024; // 10MB

  // Patient search properties
  patientSearchQuery: string = '';
  searchResults: Patient[] = [];
  selectedPatient: Patient | null = null;
  showPatientDropdown: boolean = false;
  private searchSubject = new Subject<string>();

  // Household display properties (now auto-populated based on patient)
  householdDisplayText: string = 'Nom du Foyer';
  selectedHousehold: HouseHold | null = null;
  isLoadingHousehold: boolean = false;

  // Form submission properties
  isSubmitting: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(
    private dialogRef: MatDialogRef<AddPrescriptionComponent>,
    private patientService: PatientService,
    private householdService: HouseHoldService,
    private prescriptionService: PrescriptionService
  ) {
    // Setup patient search with debouncing
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(query => {
        if (query.length >= 2) {
          return this.patientService.searchPatientsByName(query);
        } else {
          return [];
        }
      }),
      takeUntil(this.destroy$)
    ).subscribe({
      next: (patients) => {
        this.searchResults = patients;
        this.showPatientDropdown = this.patientSearchQuery.length >= 2;
        console.log('Patient search results:', patients);
      },
      error: (error) => {
        console.error('Patient search error:', error);
        this.searchResults = [];
        this.showPatientDropdown = false;
      }
    });

    // Household search is no longer needed - auto-populated based on patient selection
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Patient search methods
  onPatientSearchInput(event: Event) {
    const input = event.target as HTMLInputElement;
    this.patientSearchQuery = input.value;

    if (this.selectedPatient) {
      this.selectedPatient = null; // Clear selection when typing
    }

    this.searchSubject.next(this.patientSearchQuery);
  }

  onPatientSearchFocus() {
    if (this.patientSearchQuery.length >= 2) {
      this.showPatientDropdown = true;
    }
  }

  onPatientSearchBlur() {
    // Delay hiding dropdown to allow click on items
    setTimeout(() => {
      this.showPatientDropdown = false;
    }, 200);
  }

  selectPatient(patient: Patient) {
    this.selectedPatient = patient;
    this.patientSearchQuery = patient.fullName || `${patient.firstName} ${patient.lastName}`;
    this.showPatientDropdown = false;
    console.log('Selected patient:', patient);

    // Auto-populate household field based on patient
    this.loadPatientHousehold(patient);
  }

  // Household auto-population based on patient selection
  private loadPatientHousehold(patient: Patient) {
    if (patient.houseHoldId) {
      this.isLoadingHousehold = true;
      this.householdDisplayText = 'Chargement...';

      this.householdService.getHouseHoldById(patient.houseHoldId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (household) => {
            this.selectedHousehold = household;
            this.householdDisplayText = household.displayName || household.name;
            this.isLoadingHousehold = false;
            console.log('✅ Household loaded for patient:', household.displayName);
          },
          error: (error) => {
            console.error('❌ Error loading household:', error);
            this.selectedHousehold = null;
            this.householdDisplayText = 'Erreur de chargement';
            this.isLoadingHousehold = false;
          }
        });
    } else {
      // Patient has no household
      this.selectedHousehold = null;
      this.householdDisplayText = 'Aucun foyer trouvé';
      this.isLoadingHousehold = false;
    }
  }

  // File upload methods
  triggerFileInput() {
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      const file = input.files[0];

      // Validate file type
      if (!this.isValidFileType(file)) {
        alert('Type de fichier non supporté. Veuillez sélectionner un PDF ou une image (JPG, PNG, JPEG, GIF, BMP, WEBP).');
        this.resetFileInput();
        return;
      }

      // Validate file size
      if (file.size > this.maxFileSize) {
        alert('Le fichier est trop volumineux. La taille maximale autorisée est de 10MB.');
        this.resetFileInput();
        return;
      }

      this.uploadedFile = file;
      console.log('File selected:', file.name, 'Type:', file.type, 'Size:', this.getFileSize(file.size));
    }
  }

  removeFile(event: Event) {
    event.stopPropagation(); // Prevent triggering the file input
    this.uploadedFile = null;
    this.resetFileInput();
  }

  private resetFileInput() {
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  private isValidFileType(file: File): boolean {
    return this.acceptedImageTypes.includes(file.type) ||
           this.acceptedDocumentTypes.includes(file.type);
  }

  // Utility methods for file display
  getFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  getFileType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf': return 'PDF';
      case 'jpg':
      case 'jpeg': return 'JPEG';
      case 'png': return 'PNG';
      case 'gif': return 'GIF';
      case 'bmp': return 'BMP';
      case 'webp': return 'WEBP';
      default: return 'FILE';
    }
  }

  getFileTypeClass(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (extension === 'pdf') {
      return 'file-type-pdf';
    } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension || '')) {
      return 'file-type-image';
    }
    return 'file-type-default';
  }

  isImageFile(file: File): boolean {
    return this.acceptedImageTypes.includes(file.type);
  }

  isPdfFile(file: File): boolean {
    return file.type === 'application/pdf';
  }

  // Form validation
  isFormValid(): boolean {
    return !!(this.selectedPatient && this.uploadedFile);
  }

  // Submit prescription
  onSubmit() {
    if (!this.isFormValid()) {
      alert('Veuillez sélectionner un patient et importer un fichier.');
      return;
    }

    if (this.isSubmitting) {
      return; // Prevent double submission
    }

    // Check if selected patient has pharmacy ID
    if (!this.selectedPatient!.pharmacyId) {
      alert('Le patient sélectionné n\'a pas de pharmacie associée. Veuillez contacter l\'administrateur.');
      return;
    }

    this.isSubmitting = true;

    // Use the selected patient's pharmacy ID
    const pharmacyId = this.selectedPatient!.pharmacyId;

    console.log('Submitting prescription with:', {
      patient: this.selectedPatient,
      file: this.uploadedFile,
      pharmacyId: pharmacyId
    });

    this.prescriptionService.uploadFileAndCreatePrescription(
      this.uploadedFile!,
      this.selectedPatient!.id,
      pharmacyId,
      undefined, // note - you can add a note field if needed
      undefined  // issueDate - will default to current date
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (prescription) => {
        console.log('✅ Prescription created successfully:', prescription);
        console.log('✅ About to close modal and trigger refresh...');
        this.isSubmitting = false;

        // Trigger component refresh via listener pattern
        console.log('🔔 Triggering CreationClick event...');
        this.prescriptionService.filter('CreationClick');

        // Show success message
        alert('Ordonnance créée avec succès! Le traitement OCR est en cours...');

        // Close modal immediately with success result
        console.log('✅ Closing modal with result...');
        this.dialogRef.close({
          success: true,
          prescription: prescription,
          message: 'Prescription créée avec succès'
        });
        console.log('✅ Modal close called');
      },
      error: (error) => {
        console.error('Error creating prescription:', error);
        this.isSubmitting = false;

        // Show error message
        let errorMessage = 'Erreur lors de la création de l\'ordonnance.';
        if (error.error?.message) {
          errorMessage += ` ${error.error.message}`;
        }
        alert(errorMessage);
      }
    });
  }

  close() {
    if (this.isSubmitting) {
      const confirmClose = confirm('Une création d\'ordonnance est en cours. Êtes-vous sûr de vouloir fermer?');
      if (!confirmClose) {
        return;
      }
    }
    this.dialogRef.close();
  }
}
