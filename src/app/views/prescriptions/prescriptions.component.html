<div class="prescriptions-container">
  <!-- 🔷 Top Section: Title (left) and Icons (max right) -->
  <div class="prescriptions-header">
    <div class="title-and-icons">
      <h1 class="page-title">Liste des Ordonnances</h1>

      <div class="header-icons">
                <button class="icon-button" (click)="toggleFilter()" title="Filtrer">
          <svg cIcon name="cil-filter" class="icon"></svg>
        </button>
        <button class="icon-button" (click)="toggleView()" title="Grid">
          @if (viewMode === 'table') {
            <svg cIcon name="cil-grid" class="icon"></svg>
          } @else {
            <svg cIcon name="cil-list" class="icon"></svg>
          }
        </button>

      </div>
    </div>

    <!-- 🔷 Filter Section: Appears below icons when filter is clicked -->
    @if (showFilter()) {
      <div class="filter-container">
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">Type</label>
            <div class="custom-dropdown">
              <button class="dropdown-toggle" (click)="toggleTypeDropdown($event)">
                <span>{{ getSelectedTypeText() }}</span>
              </button>
              @if (showTypeDropdown()) {
                <div class="dropdown-menu">
                  <div class="dropdown-item" (click)="selectTypeOption('all')">
                    <span class="checkbox-square" [class.checked]="selectedType() === 'all'"></span>
                    <span class="option-text">Tous</span>
                  </div>
                  <div class="dropdown-item" (click)="selectTypeOption('foyer')">
                    <span class="checkbox-square" [class.checked]="selectedType() === 'foyer'"></span>
                    <span class="option-text">Foyer</span>
                  </div>
                </div>
              }
            </div>
          </div>

          <div class="filter-group">
            <label class="filter-label">Status</label>
            <div class="custom-dropdown">
              <button class="dropdown-toggle" (click)="toggleStatusDropdown($event)">
                <span>{{ getSelectedStatusText() }}</span>
              </button>
              @if (showStatusDropdown()) {
                <div class="dropdown-menu">
                  <div class="dropdown-item" (click)="selectStatusOption('UPLOADED')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'UPLOADED'"></span>
                    <span class="option-text">Téléchargée</span>
                  </div>
                  <div class="dropdown-item" (click)="selectStatusOption('IN_REVIEW')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'IN_REVIEW'"></span>
                    <span class="option-text">En révision</span>
                  </div>
                  <div class="dropdown-item" (click)="selectStatusOption('NEEDS_CLARIFICATION')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'NEEDS_CLARIFICATION'"></span>
                    <span class="option-text">Clarification requise</span>
                  </div>
                  <div class="dropdown-item" (click)="selectStatusOption('APPROVED')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'APPROVED'"></span>
                    <span class="option-text">Approuvée</span>
                  </div>
                  <div class="dropdown-item" (click)="selectStatusOption('REJECTED')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'REJECTED'"></span>
                    <span class="option-text">Rejetée</span>
                  </div>
                  <div class="dropdown-item" (click)="selectStatusOption('EXPIRED')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'EXPIRED'"></span>
                    <span class="option-text">Expirée</span>
                  </div>
                  <div class="dropdown-item" (click)="selectStatusOption('ARCHIVED')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'ARCHIVED'"></span>
                    <span class="option-text">Archivée</span>
                  </div>
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    }

    <!-- 🔷 Second Row: Add Button (left) and Calendar (right) -->
    <div class="action-row">
      <button class="primary-button" (click)="openAddPrescriptionModal()">
        <svg cIcon name="cil-plus" class="button-icon"></svg>
        Ajouter une ordonnance
      </button>

      <div class="calendar-container">
        <button class="nav-arrow" (click)="navigateDate(-1)">
          <svg cIcon name="cil-chevron-left"></svg>
        </button>

        <div class="date-selector">
          @for (date of dateRange; track date.getTime()) {
            <button
              class="date-button"
              [class.active]="isSameDay(date, selectedDate)"
              (click)="selectDate(date)">
              <span class="day-name">{{ getDayName(date) }}</span>
              <span class="day-number">{{ date.getDate() }}</span>
            </button>
          }
        </div>

        <button class="nav-arrow" (click)="navigateDate(1)">
          <svg cIcon name="cil-chevron-right"></svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Table View -->
  @if (viewMode === 'table') {
    <div class="table-container">
      @if (loading) {
        <div class="loading-container">
          <div class="loading-spinner"></div>
          <p>Chargement des données...</p>
        </div>
      } @else {
        <table class="prescriptions-table">
          <thead>
            <tr>
              <th>Numéro</th>
              <th>Patient</th>
              <th>Foyer</th>
              <th>Pharmacie</th>
              <th>Date</th>
              <th>Statut</th>
              <th>Approuvé</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            @for (prescription of prescriptions; track prescription.id) {
            <tr>
              <td>{{ prescription.patientPhone || 'N/A' }}</td>
              <td class="patient-cell">
                <div class="patient-info">
                  <div class="initials-badge">{{ prescription.patientInitials || 'PA' }}</div>
                  <span>{{ prescription.patientName || 'Patient #' + prescription.patientId }}</span>
                </div>
              </td>
              <td>
                @if (prescription.householdName) {
                  <span>{{ prescription.householdName }}</span>
                } @else {
                  <span class="empty-state">----------</span>
                }
              </td>
              <td>
                @if (prescription.pharmacyName) {
                  <span>{{ prescription.pharmacyName }}</span>
                } @else {
                  <span class="empty-state">----------</span>
                }
              </td>
              <td>{{ formatDate(prescription.date) }}</td>
              <td>
                <span class="badge" [ngClass]="getBadgeClass(prescription.prescriptionStatus)">
                  {{ getBadgeText(prescription.prescriptionStatus) }}
                </span>
              </td>
              <td>
                <span class="badge" [ngClass]="prescription.approved ? 'badge-approved' : 'badge-pending'">
                  {{ prescription.approved ? 'Approuvée' : 'En attente' }}
                </span>
              </td>
              <td class="actions-cell">
                <button title="Voir" (click)="viewDetails(prescription)">
                  <img src="assets/images/EyeIcon.svg" alt="Voir" class="eye-icon" />
                </button>
                <button title="Télécharger">
                  <svg cIcon name="cil-cloud-download"></svg>
                </button>
              </td>
            </tr>
          }
        </tbody>
      </table>
      }
    </div>
  } @else {
    <!-- Cards View -->
    <div class="cards-container">
      @if (loading) {
        <div class="loading-container">
          <div class="loading-spinner"></div>
          <p>Chargement des données...</p>
        </div>
      } @else {
        @for (prescription of prescriptions; track prescription.id) {
        <div class="prescription-card">
          <div class="card-header">
            <div class="patient-info">
              <div class="initials-badge">{{ prescription.patientInitials || 'PA' }}</div>
              <div class="patient-details">
                <h3 class="patient-name">{{ prescription.patientName || 'Patient #' + prescription.patientId }}</h3>
                <p class="patient-foyer">
                  <span class="foyer-label">Foyer :</span>
                  @if (prescription.householdName) {
                    <span class="foyer-value">{{ prescription.householdName }}</span>
                  } @else {
                    <span class="foyer-value">----------</span>
                  }
                </p>
                <p class="patient-pharmacy">
                  <span class="pharmacy-label">Pharmacie :</span>
                  @if (prescription.pharmacyName) {
                    <span class="pharmacy-value">{{ prescription.pharmacyName }}</span>
                  } @else {
                    <span class="pharmacy-value">Pharmacie #{{ prescription.pharmacyId }}</span>
                  }
                </p>
              </div>
            </div>
            <div class="card-actions">
              <div class="menu-container">
                <button class="menu-toggle" (click)="toggleCardMenu(prescription.id)">
                  <svg cIcon name="cil-options" class="ellipsis-icon"></svg>
                </button>
                @if (activeCardMenu === prescription.id) {
                  <div class="action-menu">
                    <button class="menu-item" (click)="viewDetails(prescription)">Voir Détails</button>
                    <button class="menu-item" (click)="downloadPrescription(prescription)">Télécharger</button>
                  </div>
                }
              </div>
            </div>
          </div>

          <!-- Separator Line -->
          <div class="card-separator"></div>

          <div class="card-content">
            <div class="details-grid">
              <div class="detail-item">
                <span class="detail-label">Numéro :</span>
                <span class="detail-value">{{ prescription.patientPhone || 'N/A' }}</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">Statut :</span>
                <span class="badge" [ngClass]="getBadgeClass(prescription.prescriptionStatus)">
                  {{ getBadgeText(prescription.prescriptionStatus) }}
                </span>
              </div>

              <div class="detail-item">
                <span class="detail-label">Approuvé :</span>
                <span class="badge" [ngClass]="prescription.approved ? 'badge-approved' : 'badge-pending'">
                  {{ prescription.approved ? 'Oui' : 'Non' }}
                </span>
              </div>

              <div class="detail-item">
                <span class="detail-label">Date :</span>
                <span class="detail-value">{{ formatDate(prescription.date) }}</span>
              </div>
            </div>
          </div>
        </div>
        }
      }
    </div>
  }

  <!-- Pagination Section -->
  @if (getTotalCount() > 0) {
    <div class="pagination-container">
      <!-- Items per page selector -->
      <div class="items-per-page">
        <label class="pagination-label">Éléments par page :</label>
        <select class="page-size-selector" [value]="itemsPerPage" (change)="onPageSizeChange($event)">
          @for (size of availablePageSizes; track size) {
            <option [value]="size">{{ size }}</option>
          }
        </select>
      </div>

      <!-- Pagination info -->
      <div class="pagination-info">
        <span class="pagination-text">
          {{ (currentPage - 1) * itemsPerPage + 1 }} -
          {{ getEndItemNumber() }}
          sur {{ getTotalCount() }} ordonnances
        </span>
      </div>

      <!-- Pagination controls -->
      <div class="pagination-controls">
        <!-- First page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === 1"
          (click)="goToFirstPage()"
          title="Première page">
          <svg cIcon name="cil-media-skip-backward"></svg>
        </button>

        <!-- Previous page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === 1"
          (click)="goToPreviousPage()"
          title="Page précédente">
          <svg cIcon name="cil-chevron-left"></svg>
        </button>

        <!-- Page numbers -->
        @for (page of getPageNumbers(); track page) {
          <button
            class="pagination-btn page-number"
            [class.active]="page === currentPage"
            (click)="goToPage(page)">
            {{ page }}
          </button>
        }

        <!-- Next page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === totalPages"
          (click)="goToNextPage()"
          title="Page suivante">
          <svg cIcon name="cil-chevron-right"></svg>
        </button>

        <!-- Last page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === totalPages"
          (click)="goToLastPage()"
          title="Dernière page">
          <svg cIcon name="cil-media-skip-forward"></svg>
        </button>
      </div>
    </div>
  }
</div>
