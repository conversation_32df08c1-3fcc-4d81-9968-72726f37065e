.notifications-page {
  .page-header {
    .page-title {
      font-family: 'Nautica Rounded', sans-serif;
      font-weight: 800;
      color: #163659;
      margin-bottom: 8px;
    }

    .page-subtitle {
      color: #728A9B;
      font-size: 1rem;
      margin-bottom: 0;
    }
  }

  .card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  .status-item {
    .form-label {
      font-weight: 500;
      margin-bottom: 4px;
    }
  }

  .action-buttons {
    .btn {
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      &:disabled {
        opacity: 0.6;
        transform: none;
        box-shadow: none;
      }
    }
  }

  .notifications-list {
    .notification-item {
      transition: all 0.2s ease;
      background-color: #FFFFFF;

      &:hover {
        background-color: #F5FFF9;
        border-color: #57B6B1 !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      &.unread {
        background-color: #F8F9FA;
        border-left: 4px solid #1061AC !important;

        .notification-title {
          font-weight: 700;
        }
      }

      .notification-icon {
        .icon-circle {
          transition: all 0.2s ease;
        }
      }

      &:hover .notification-icon .icon-circle {
        background-color: #57B6B1;
        border-color: #1061AC;

        i {
          color: white !important;
        }
      }

      .notification-title {
        font-size: 1rem;
        line-height: 1.3;
      }

      .notification-message {
        font-size: 0.9rem;
        line-height: 1.4;
      }

      .notification-meta {
        font-size: 0.8rem;
      }

      .unread-indicator {
        .rounded-circle {
          animation: pulse 2s infinite;
        }
      }
    }
  }
}

// Pulse animation
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// Responsive design
@media (max-width: 768px) {
  .notifications-page {
    .action-buttons {
      .btn {
        margin-bottom: 8px;
        width: 100%;
      }
    }

    .notification-item {
      .d-flex {
        flex-direction: column;

        .notification-icon {
          margin-bottom: 12px;
          align-self: center;
        }

        .notification-meta {
          text-align: center;
          margin-top: 12px;
        }
      }
    }
  }
}