import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { NotificationService } from '../../services/notification.service';
import { FirebaseService } from '../../services/firebase.service';
import {
  Notification,
  NotificationTypeTranslations,
  NotificationStatusTranslations
} from '../../models/notification.model';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './notifications.component.html',
  styleUrl: './notifications.component.scss'
})
export class NotificationsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  notifications: Notification[] = [];
  unreadCount: number = 0;
  loading: boolean = false;
  fcmToken: string | null = null;
  notificationPermission: NotificationPermission = 'default';

  constructor(
    private notificationService: NotificationService,
    private firebaseService: FirebaseService
  ) {}

  ngOnInit(): void {
    // Subscribe to notifications
    this.notificationService.notifications$
      .pipe(takeUntil(this.destroy$))
      .subscribe(notifications => {
        this.notifications = notifications;
      });

    // Subscribe to unread count
    this.notificationService.unreadCount$
      .pipe(takeUntil(this.destroy$))
      .subscribe(count => {
        this.unreadCount = count;
      });

    // Subscribe to loading state
    this.notificationService.loading$
      .pipe(takeUntil(this.destroy$))
      .subscribe(loading => {
        this.loading = loading;
      });

    // Get FCM token
    this.fcmToken = this.firebaseService.getCurrentToken();
    this.notificationPermission = this.firebaseService.getNotificationPermission();

    // Load notifications
    this.loadNotifications();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadNotifications(): void {
    this.notificationService.getUserNotifications({ limit: 50 }).subscribe();
  }

  markAsRead(notification: Notification): void {
    if (notification.status === 'UNREAD') {
      this.notificationService.markAsRead(notification.id).subscribe();
    }
  }

  markAllAsRead(): void {
    this.notificationService.markAllAsRead().subscribe();
  }

  refreshNotifications(): void {
    this.notificationService.refreshNotifications();
  }

  async requestNotificationPermission(): Promise<void> {
    const hasPermission = await this.firebaseService.requestPermission();
    this.notificationPermission = this.firebaseService.getNotificationPermission();

    if (hasPermission) {
      const token = await this.firebaseService.getFcmToken();
      this.fcmToken = token;
    }
  }

  testNotification(): void {
    this.firebaseService.showTestNotification(
      'Test Notification',
      'This is a test notification from Med4 Solutions',
      { type: 'GENERAL' }
    );
  }

  getNotificationTypeLabel(type: string): string {
    return NotificationTypeTranslations[type as keyof typeof NotificationTypeTranslations] || type;
  }

  getNotificationStatusLabel(status: string): string {
    return NotificationStatusTranslations[status as keyof typeof NotificationStatusTranslations] || status;
  }

  formatDate(date: string | Date): string {
    return new Date(date).toLocaleString('fr-FR');
  }
}
