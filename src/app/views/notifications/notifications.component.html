<div class="notifications-page">
  <!-- Page Header -->
  <div class="page-header mb-4">
    <h1 class="page-title" style="color: #163659; font-weight: 800;">Notifications</h1>
    <p class="page-subtitle" style="color: #728A9B;">Gérez vos notifications et préférences</p>
  </div>

  <!-- Notification Status Card -->
  <div class="card mb-4" style="background: #FFFFFF; border: 1px solid #C7DAEC; border-radius: 10px;">
    <div class="card-body p-4">
      <h5 class="card-title mb-3" style="color: #163659; font-weight: 600;">État des Notifications</h5>

      <div class="row">
        <div class="col-md-6">
          <div class="status-item mb-3">
            <label class="form-label" style="color: #163659; font-weight: 500;">Permission du navigateur:</label>
            <span class="ms-2" [class]="'badge ' + (notificationPermission === 'granted' ? 'bg-success' : notificationPermission === 'denied' ? 'bg-danger' : 'bg-warning')">
              {{ notificationPermission === 'granted' ? 'Accordée' : notificationPermission === 'denied' ? 'Refusée' : 'En attente' }}
            </span>
          </div>

          <div class="status-item mb-3">
            <label class="form-label" style="color: #163659; font-weight: 500;">Token FCM:</label>
            <div class="mt-1">
              @if (fcmToken) {
                <small class="text-muted font-monospace">{{ fcmToken.substring(0, 50) }}...</small>
              } @else {
                <span class="text-muted">Non disponible</span>
              }
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="status-item mb-3">
            <label class="form-label" style="color: #163659; font-weight: 500;">Notifications non lues:</label>
            <span class="ms-2 badge" style="background-color: #1061AC; color: white;">{{ unreadCount }}</span>
          </div>

          <div class="status-item mb-3">
            <label class="form-label" style="color: #163659; font-weight: 500;">Total des notifications:</label>
            <span class="ms-2 text-muted">{{ notifications.length }}</span>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons mt-3">
        @if (notificationPermission !== 'granted') {
          <button
            class="btn me-2"
            style="background: linear-gradient(135deg, #1061AC 0%, #57B6B1 100%); color: white; border: none; border-radius: 6px; padding: 8px 16px;"
            (click)="requestNotificationPermission()"
          >
            Autoriser les notifications
          </button>
        }

        <button
          class="btn me-2"
          style="background-color: #1061AC; color: white; border: none; border-radius: 6px; padding: 8px 16px;"
          (click)="testNotification()"
        >
          Tester une notification
        </button>

        <button
          class="btn me-2"
          style="background-color: #57B6B1; color: white; border: none; border-radius: 6px; padding: 8px 16px;"
          (click)="refreshNotifications()"
          [disabled]="loading"
        >
          @if (loading) {
            <span class="spinner-border spinner-border-sm me-1"></span>
          }
          Actualiser
        </button>

        @if (unreadCount > 0) {
          <button
            class="btn"
            style="background-color: #F80D38; color: white; border: none; border-radius: 6px; padding: 8px 16px;"
            (click)="markAllAsRead()"
          >
            Marquer tout comme lu
          </button>
        }
      </div>
    </div>
  </div>

  <!-- Notifications List -->
  <div class="card" style="background: #FFFFFF; border: 1px solid #C7DAEC; border-radius: 10px;">
    <div class="card-body p-4">
      <h5 class="card-title mb-3" style="color: #163659; font-weight: 600;">Historique des Notifications</h5>

      @if (loading && notifications.length === 0) {
        <div class="text-center py-4">
          <div class="spinner-border" style="color: #1061AC;"></div>
          <p class="mt-2 text-muted">Chargement des notifications...</p>
        </div>
      } @else if (notifications.length === 0) {
        <div class="text-center py-4">
          <div class="mb-3">
            <i class="fas fa-bell fa-3x text-muted"></i>
          </div>
          <p class="text-muted">Aucune notification disponible</p>
        </div>
      } @else {
        <div class="notifications-list">
          @for (notification of notifications; track notification.id) {
            <div
              class="notification-item p-3 mb-3 rounded"
              [class.unread]="notification.status === 'UNREAD'"
              style="border: 1px solid #E6EDF5; cursor: pointer; transition: all 0.2s ease;"
              (click)="markAsRead(notification)"
            >
              <div class="d-flex align-items-start">
                <div class="notification-icon me-3 mt-1">
                  <div
                    class="icon-circle d-flex align-items-center justify-content-center"
                    style="width: 40px; height: 40px; border-radius: 50%; background-color: #F5FFF9; border: 2px solid #57B6B1;"
                  >
                    <i class="fas fa-bell" style="color: #1061AC;"></i>
                  </div>
                </div>

                <div class="notification-content flex-grow-1">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="notification-title mb-0" style="color: #163659; font-weight: 600;">
                      {{ notification.title }}
                    </h6>
                    <div class="notification-meta text-end">
                      <small class="text-muted d-block">{{ formatDate(notification.createdAt || '') }}</small>
                      <span
                        class="badge mt-1"
                        [style.background-color]="notification.status === 'UNREAD' ? '#1061AC' : '#728A9B'"
                        style="color: white; font-size: 0.7rem;"
                      >
                        {{ getNotificationStatusLabel(notification.status) }}
                      </span>
                    </div>
                  </div>

                  <p class="notification-message mb-2" style="color: #728A9B; margin-bottom: 8px;">
                    {{ notification.message }}
                  </p>

                  <div class="notification-footer d-flex justify-content-between align-items-center">
                    <span class="notification-type badge" style="background-color: #C7DAEC; color: #163659; font-size: 0.75rem;">
                      {{ getNotificationTypeLabel(notification.type) }}
                    </span>

                    @if (notification.status === 'UNREAD') {
                      <div class="unread-indicator">
                        <div
                          class="rounded-circle"
                          style="width: 8px; height: 8px; background-color: #1061AC; animation: pulse 2s infinite;"
                        ></div>
                      </div>
                    }
                  </div>
                </div>
              </div>
            </div>
          }
        </div>
      }
    </div>
  </div>
</div>
