<div class="order-details-container">
  <!-- Navigation will be inside the first container -->

  <!-- Loading State -->
  @if (loading) {
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Chargement des détails de la commande...</p>
    </div>
  }

  <!-- Error State -->
  @if (error) {
    <div class="error-container">
      <p>{{ error }}</p>
      <button class="retry-button" (click)="loadOrderDetails()">Réessayer</button>
    </div>
  }

  <!-- Order Details Content -->
  @if (order && !loading && !error) {
    <div class="details-content" [class.narrow-mode]="isNarrowMode">
      <!-- Left Container - Order Summary (Single Cohesive Container) -->
      <div class="order-summary-container">
        <!-- Navigation Header -->
        <div class="navigation-header">
          <div class="nav-left">
            <button class="back-button" (click)="goBack()">
              <span class="back-arrow">&lt;</span>
              <span>Liste des Commandes</span>
            </button>
          </div>
          <!-- Action Buttons -->
          <div class="nav-right">
            <!-- Confirmer Button (only for PENDING orders) -->
            @if (order && order.status === 'PENDING') {
              <button class="confirm-button" (click)="confirmOrder(order)">Confirmer</button>
            }
            <!-- Marquer comme livrée Button (only for APPROVED and IN_TRANSIT orders) -->
            @if (order && (order.status === 'APPROVED' || order.status === 'IN_TRANSIT')) {
              <button class="action-btn primary" (click)="markAsDelivered()">Marquer comme livrée</button>
            }
          </div>
        </div>
        <div class="page-title">Détails de Commande</div>

        <!-- Prix Total Box -->
        <div class="prix-total-box">
          <div class="price-row">
            <div class="price-left">
              <div class="price-label">Prix Total</div>
              <div class="patient-address">
                @if (patient && patient.address && patient.address.length > 0) {
                  @for (addr of patient.address; track $index) {
                    <div class="address-item">
                      <svg class="address-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                        <circle cx="12" cy="10" r="3"/>
                      </svg>
                      <span>{{ addr.address }}</span>
                    </div>
                  }
                } @else {
                  <div class="address-item">
                    <svg class="address-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                      <circle cx="12" cy="10" r="3"/>
                    </svg>
                    <span>Adresse non disponible</span>
                  </div>
                }
              </div>
            </div>
            <div class="price-right">
              <div class="price-amount">{{ formatAmount(order.totalAmount) }}</div>
              <div class="payment-type">{{ getPaymentTypeText() }}</div>
            </div>
          </div>
        </div>

        <!-- Articles Sélectionnés Table -->
        <div class="articles-section">
          <h3 class="articles-title">Articles Sélectionnés</h3>

          @if (loadingProducts) {
            <div class="loading-products">
              <div class="loading-spinner-small"></div>
              <span>Chargement des produits...</span>
            </div>
          } @else if (orderProducts.length > 0) {
            <div class="articles-table">
              <table>
                <thead>
                  <tr>
                    <th>Articles Sélectionnés</th>
                    <th>Nom</th>
                    <th>Quantité</th>
                    <th>Prix Unitaire</th>
                  </tr>
                </thead>
                <tbody>
                  @for (product of orderProducts; track product.id) {
                    <tr>
                      <td class="product-image-cell">
                        <div class="table-image-container">
                          <img
                            [src]="getProductImageUrl(product)"
                            [alt]="product.name"
                            class="product-image"
                          />

                          <!-- Navigation arrows for multiple images -->
                          @if (hasMultipleImages(product)) {
                            <button
                              class="table-nav-arrow table-nav-arrow-left"
                              (click)="previousImage(product, $event)"
                              [disabled]="!canNavigatePrevious(product)"
                              type="button"
                              title="Image précédente">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"></polyline>
                              </svg>
                            </button>
                            <button
                              class="table-nav-arrow table-nav-arrow-right"
                              (click)="nextImage(product, $event)"
                              [disabled]="!canNavigateNext(product)"
                              type="button"
                              title="Image suivante">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"></polyline>
                              </svg>
                            </button>

                            <!-- Image counter -->
                            <div class="table-image-counter">{{ getImageCounter(product) }}</div>
                          }
                        </div>
                      </td>
                      <td class="product-name">{{ product.name }}</td>
                      <td class="product-quantity">{{ product.orderQuantity }}</td>
                      <td class="product-price">{{ formatAmount(product.price) }}</td>
                    </tr>
                  }
                </tbody>
              </table>
            </div>
          } @else {
            <div class="no-products">
              <p>Aucun produit trouvé</p>
            </div>
          }
        </div>
      </div>

      <!-- Right Container - Patient Information -->
      <div class="patient-info-container">
        <div class="patient-card">
          <div class="card-header">
            <h2 class="card-title">Informations du patient</h2>
            <!-- Narrow Mode Toggle Button (Sidebar style) -->
            <button class="narrow-toggle" (click)="toggleNarrowMode()" [title]="isNarrowMode ? 'Élargir' : 'Réduire'">
              <img src="assets/images/narrow.svg" alt="Toggle narrow mode" class="narrow-icon" />
            </button>
          </div>
          <div class="card-content">
            @if (loadingPatient) {
              <div class="loading-patient">
                <div class="loading-spinner-small"></div>
                <span>Chargement des informations patient...</span>
              </div>
            } @else if (patient) {
              <div class="patient-info">
                <!-- Identity Section -->
                <div class="identity-section">
                  <div class="patient-avatar">
                    <div class="initials-badge">{{ patient.initials || 'PA' }}</div>
                  </div>
                  <div class="patient-name">{{ patient.fullName }}</div>
                  <div class="patient-separator"></div>
                </div>

                <!-- Patient Data Grid -->
                <div class="patient-data-grid">
                  <!-- Row 1: Email and Phone -->
                  <div class="data-row">
                    <div class="data-column">
                      <div class="data-label">E-mail</div>
                      <div class="data-value">{{ patient.email }}</div>
                    </div>
                    <div class="data-column">
                      <div class="data-label">N° de téléphone</div>
                      <div class="data-value">{{ patient.phone }}</div>
                    </div>
                  </div>

                  <!-- Row 2: Address and Household -->
                  <div class="data-row">
                    <div class="data-column">
                      <div class="data-label">Adresse</div>
                      <div class="data-value address-value">
                        @if (patient.address && patient.address.length > 0) {
                          @for (addr of patient.address; track $index) {
                            <div class="address-item">
                              <svg class="address-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                <circle cx="12" cy="10" r="3"/>
                              </svg>
                              <span>{{ addr.address }}</span>
                            </div>
                          }
                        } @else {
                          <span>Adresse non disponible</span>
                        }
                      </div>
                    </div>
                    <div class="data-column">
                      <div class="data-label">Foyer</div>
                      <div class="data-value">{{ getHouseholdName() }}</div>
                    </div>
                  </div>

                  <!-- Row 3: Hospitalization and Vacation -->
                  <div class="data-row">
                    <div class="data-column">
                      <div class="data-label">Patient hospitalisé</div>
                      <div class="data-value">{{ getHospitalizationStatus() }}</div>
                    </div>
                    <div class="data-column">
                      <div class="data-label">Patient en vacances</div>
                      <div class="data-value">{{ getVacationStatus() }}</div>
                    </div>
                  </div>
                </div>
              </div>
            } @else {
              <div class="no-patient">
                <p>Informations patient non disponibles</p>
              </div>
            }
          </div>
        </div>
      </div>
    </div>
  }

  <!-- Confirmation Modal -->
  @if (showConfirmModal && order) {
    <div class="modal-overlay" (click)="closeConfirmModal()">
      <div class="modal-container" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h2 class="modal-title">Confirmer la commande</h2>
          <button class="close-button" (click)="closeConfirmModal()" type="button"></button>
        </div>

        <div class="modal-content">
          <div class="order-info">
            <h3>Commande de {{ patient?.fullName || order.patientName || 'Patient #' + order.patientId }}</h3>
            @if (household?.name || order.householdName) {
              <p class="household-info">Foyer: {{ household?.name || order.householdName }}</p>
            }
          </div>

          <!-- Product Stock Information -->
          @if (loadingProductInfo) {
            <div class="product-loading">
              <div class="loading-spinner-small"></div>
              <span>Chargement des informations du produit...</span>
            </div>
          } @else if (availableProductQuantity > 0) {
            <div class="product-stock-info">
              <span class="stock-label">Stock disponible:</span>
              <span class="stock-value">{{ availableProductQuantity }} unité(s)</span>
            </div>
          }

          <div class="form-group">
            <label for="confirmQuantity" class="form-label">Quantité *</label>
            <input
              id="confirmQuantity"
              type="number"
              class="form-input"
              [class.error]="productValidationError"
              [(ngModel)]="confirmQuantity"
              (ngModelChange)="validateQuantity()"
              min="1"
              [max]="availableProductQuantity"
              required>

            @if (productValidationError) {
              <div class="error-message">{{ productValidationError }}</div>
            }
          </div>

          <div class="form-group">
            <label for="confirmDeliveryNote" class="form-label">Note du livreur</label>
            <textarea
              id="confirmDeliveryNote"
              class="form-textarea"
              [(ngModel)]="confirmDeliveryNote"
              placeholder="Note optionnelle pour le livreur..."
              rows="3"></textarea>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn-cancel" (click)="closeConfirmModal()">
            Annuler
          </button>
          <button
            class="btn-confirm"
            (click)="submitPharmacistConfirmation()"
            [disabled]="!isConfirmationValid()">
            @if (loadingProductInfo) {
              Chargement...
            } @else {
              Valider
            }
          </button>
        </div>
      </div>
    </div>
  }
