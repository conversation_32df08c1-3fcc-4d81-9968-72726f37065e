<div class="orders-container">
  <!-- 🔷 Top Section: Title (left) and Icons (max right) -->
  <div class="orders-header">
    <div class="title-and-icons">
      <h1 class="page-title">Liste des Commandes</h1>

      <div class="header-icons">
        <button class="icon-button" (click)="toggleFilter()" title="Filtrer">
          <svg cIcon name="cil-filter" class="icon"></svg>
        </button>
        <button class="icon-button" (click)="toggleView()" title="Grid">
          @if (viewMode === 'table') {
            <svg cIcon name="cil-grid" class="icon"></svg>
          } @else {
            <svg cIcon name="cil-list" class="icon"></svg>
          }
        </button>
      </div>
    </div>

    <!-- 🔷 Filter Section: Appears below icons when filter is clicked -->
    @if (showFilter()) {
      <div class="filter-container">
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">Type</label>
            <div class="custom-dropdown">
              <button class="dropdown-toggle" (click)="toggleTypeDropdown($event)">
                <span>{{ getSelectedTypeText() }}</span>
              </button>
              @if (showTypeDropdown()) {
                <div class="dropdown-menu">
                  <div class="dropdown-item" (click)="selectTypeOption('all')">
                    <span class="checkbox-square" [class.checked]="selectedType() === 'all'"></span>
                    <span class="option-text">Tous</span>
                  </div>
                  <div class="dropdown-item" (click)="selectTypeOption('patient')">
                    <span class="checkbox-square" [class.checked]="selectedType() === 'patient'"></span>
                    <span class="option-text">Patient</span>
                  </div>
                  <div class="dropdown-item" (click)="selectTypeOption('foyer')">
                    <span class="checkbox-square" [class.checked]="selectedType() === 'foyer'"></span>
                    <span class="option-text">Foyer</span>
                  </div>
                </div>
              }
            </div>
          </div>

          <div class="filter-group">
            <label class="filter-label">Statut</label>
            <div class="custom-dropdown">
              <button class="dropdown-toggle" (click)="toggleStatusDropdown($event)">
                <span>{{ getSelectedStatusText() }}</span>
              </button>
              @if (showStatusDropdown()) {
                <div class="dropdown-menu">
                  <div class="dropdown-item" (click)="selectStatusOption('')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === ''"></span>
                    <span class="option-text">Tous les statuts</span>
                  </div>
                  <div class="dropdown-item" (click)="selectStatusOption('PENDING')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'PENDING'"></span>
                    <span class="option-text">En attente</span>
                  </div>
                  <div class="dropdown-item" (click)="selectStatusOption('APPROVED')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'APPROVED'"></span>
                    <span class="option-text">Approuvée</span>
                  </div>
                  <div class="dropdown-item" (click)="selectStatusOption('IN_TRANSIT')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'IN_TRANSIT'"></span>
                    <span class="option-text">En transit</span>
                  </div>
                  <div class="dropdown-item" (click)="selectStatusOption('DELIVERED')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'DELIVERED'"></span>
                    <span class="option-text">Livrée</span>
                  </div>
                  <div class="dropdown-item" (click)="selectStatusOption('CANCELLED')">
                    <span class="checkbox-square" [class.checked]="selectedStatus() === 'CANCELLED'"></span>
                    <span class="option-text">Annulée</span>
                  </div>
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    }

    <!-- 🔷 Second Row: Calendar (right) - matching prescription component -->
    <div class="action-row">
      <div class="calendar-container">
        <button class="nav-arrow" (click)="navigateDate(-1)">
          <svg cIcon name="cil-chevron-left"></svg>
        </button>

        <div class="date-selector">
          @for (date of dateRange; track date.getTime()) {
            <button
              class="date-button"
              [class.active]="isSameDay(date, selectedDate)"
              (click)="selectDate(date)">
              <span class="day-name">{{ getDayName(date) }}</span>
              <span class="day-number">{{ date.getDate() }}</span>
            </button>
          }
        </div>

        <button class="nav-arrow" (click)="navigateDate(1)">
          <svg cIcon name="cil-chevron-right"></svg>
        </button>
      </div>
    </div>
  </div>

  <!-- 🔷 Loading State -->
  @if (loading) {
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Chargement des commandes...</p>
    </div>
  }

  <!-- 🔷 Error State -->
  @if (error) {
    <div class="error-container">
      <p>{{ error }}</p>
      <button class="retry-button" (click)="loadOrders()">Réessayer</button>
    </div>
  }

  <!-- 🔷 Content Section -->
  @if (!loading && !error) {
    <!-- Table View -->
    @if (viewMode === 'table') {
      <div class="table-container">
        <table class="orders-table">
          <thead>
            <tr>
              <th>Numéro</th>
              <th>Patient</th>
              <th>Foyer</th>
              <th>Date</th>
              <th>Montant Total</th>
              <th>Statut Commande</th>
              <th>Confirmation</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            @for (order of orders; track order.id) {
              <tr>
                <td>{{ order.patientPhone || 'N/A' }}</td>
                <td class="patient-cell">
                  <div class="patient-info">
                    <div class="initials-badge">{{ order.patientInitials || 'PA' }}</div>
                    <span>{{ order.patientName || 'Patient #' + order.patientId }}</span>
                  </div>
                </td>
                <td>
                  @if (order.householdName) {
                    <span>{{ order.householdName }}</span>
                  } @else {
                    <span class="empty-state">----------</span>
                  }
                </td>
                <td>{{ formatDate(order.orderDate) }}</td>
                <td class="amount-cell">{{ formatAmount(order.totalAmount) }}</td>
                <td>
                  <span class="badge" [ngClass]="getBadgeClass(order.status)">
                    {{ getBadgeText(order.status) }}
                  </span>
                </td>
                <td class="confirmation-cell">
                  @if (order.status === 'PENDING') {
                    <button
                      class="confirm-button"
                      (click)="confirmOrder(order)"
                      title="Confirmer commande">
                      Confirmer commande
                    </button>
                  } @else {
                    <span class="confirmed-text">Confirmée</span>
                  }
                </td>
                <td class="actions-cell">
                  <button title="Voir" (click)="viewDetails(order)">
                    <img src="assets/images/EyeIcon.svg" alt="Voir" class="eye-icon" />
                  </button>
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    } @else {
      <!-- Cards View -->
      <div class="cards-container">
        @for (order of orders; track order.id) {
          <div class="order-card">
            <div class="card-header">
              <div class="patient-info">
                <div class="initials-badge">{{ order.patientInitials || 'PA' }}</div>
                <div class="patient-details">
                  <div class="patient-name">{{ order.patientName || 'Patient #' + order.patientId }}</div>
                  @if (order.householdName) {
                    <div class="household-name">{{ order.householdName }}</div>
                  } @else {
                    <div class="household-name empty">Aucun foyer</div>
                  }
                  <div class="household-separator"></div>
                </div>
              </div>
              <div class="card-menu">
                <button class="menu-button" (click)="toggleCardMenu(order.id)">
                  <svg cIcon name="cil-options" class="menu-icon"></svg>
                </button>
                @if (activeCardMenu === order.id) {
                  <div class="menu-dropdown">
                    <button class="menu-item" (click)="viewDetails(order)">
                      <svg cIcon name="cil-eye" class="menu-item-icon"></svg>
                      <span>Voir Détails</span>
                    </button>
                  </div>
                }
              </div>
            </div>
            <div class="card-content">
              <div class="order-info">
                <div class="info-row">
                  <span class="label">Numéro:</span>
                  <span class="value">{{ order.patientPhone || 'N/A' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">Date:</span>
                  <span class="value">{{ formatDate(order.orderDate) }}</span>
                </div>
                <div class="info-row">
                  <span class="label">Montant:</span>
                  <span class="value amount">{{ formatAmount(order.totalAmount) }}</span>
                </div>
              </div>
              <div class="card-footer">
                <span class="badge" [ngClass]="getBadgeClass(order.status)">
                  {{ getBadgeText(order.status) }}
                </span>
                @if (order.status === 'PENDING') {
                  <button
                    class="confirm-button-small"
                    (click)="confirmOrder(order)"
                    title="Confirmer commande">
                    Confirmer
                  </button>
                }
              </div>
            </div>
          </div>
        }
      </div>
    }

  <!-- Pagination Section -->
  @if (getTotalCount() > 0) {
    <div class="pagination-container">
      <!-- Items per page selector -->
      <div class="items-per-page">
        <label class="pagination-label">Éléments par page :</label>
        <select class="page-size-selector" [value]="itemsPerPage" (change)="onPageSizeChange($event)">
          @for (size of availablePageSizes; track size) {
            <option [value]="size">{{ size }}</option>
          }
        </select>
      </div>

      <!-- Pagination info -->
      <div class="pagination-info">
        <span class="pagination-text">
          {{ (currentPage - 1) * itemsPerPage + 1 }} -
          {{ getEndItemNumber() }}
          sur {{ getTotalCount() }} commandes
        </span>
      </div>

      <!-- Pagination controls -->
      <div class="pagination-controls">
        <!-- First page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === 1"
          (click)="goToFirstPage()"
          title="Première page">
          <svg cIcon name="cil-media-skip-backward"></svg>
        </button>

        <!-- Previous page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === 1"
          (click)="goToPreviousPage()"
          title="Page précédente">
          <svg cIcon name="cil-chevron-left"></svg>
        </button>

        <!-- Page numbers -->
        @for (page of getPageNumbers(); track page) {
          <button
            class="pagination-btn page-number"
            [class.active]="page === currentPage"
            (click)="goToPage(page)">
            {{ page }}
          </button>
        }

        <!-- Next page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === totalPages"
          (click)="goToNextPage()"
          title="Page suivante">
          <svg cIcon name="cil-chevron-right"></svg>
        </button>

        <!-- Last page -->
        <button
          class="pagination-btn"
          [disabled]="currentPage === totalPages"
          (click)="goToLastPage()"
          title="Dernière page">
          <svg cIcon name="cil-media-skip-forward"></svg>
        </button>
      </div>
    </div>
  }

  <!-- Confirmation Modal -->
  @if (showConfirmModal && selectedOrderForConfirm) {
    <div class="modal-overlay" (click)="closeConfirmModal()">
      <div class="modal-container" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h2 class="modal-title">Confirmer la commande</h2>
          <button class="close-button" (click)="closeConfirmModal()" type="button"></button>
        </div>

        <div class="modal-content">
          <div class="order-info">
            <h3>Commande de {{ selectedOrderForConfirm.patientName || 'Patient #' + selectedOrderForConfirm.patientId }}</h3>
            @if (selectedOrderForConfirm.householdName) {
              <p class="household-info">Foyer: {{ selectedOrderForConfirm.householdName }}</p>
            }
          </div>

          <!-- Product Stock Information -->
          @if (loadingProductInfo) {
            <div class="product-loading">
              <div class="loading-spinner-small"></div>
              <span>Chargement des informations du produit...</span>
            </div>
          } @else if (availableProductQuantity > 0) {
            <div class="product-stock-info">
              <span class="stock-label">Stock disponible:</span>
              <span class="stock-value">{{ availableProductQuantity }} unité(s)</span>
            </div>
          }

          <div class="form-group">
            <label for="confirmQuantity" class="form-label">Quantité *</label>
            <input
              id="confirmQuantity"
              type="number"
              class="form-input"
              [class.error]="productValidationError"
              [(ngModel)]="confirmQuantity"
              (ngModelChange)="validateQuantity()"
              min="1"
              [max]="availableProductQuantity"
              required>

            @if (productValidationError) {
              <div class="error-message">{{ productValidationError }}</div>
            }
          </div>

          <div class="form-group">
            <label for="confirmDeliveryNote" class="form-label">Note du livreur</label>
            <textarea
              id="confirmDeliveryNote"
              class="form-textarea"
              [(ngModel)]="confirmDeliveryNote"
              placeholder="Note optionnelle pour le livreur..."
              rows="3"></textarea>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn-cancel" (click)="closeConfirmModal()">
            Annuler
          </button>
          <button
            class="btn-confirm"
            (click)="submitPharmacistConfirmation()"
            [disabled]="!isConfirmationValid()">
            @if (loadingProductInfo) {
              Chargement...
            } @else {
              Valider
            }
          </button>
        </div>
      </div>
    </div>
  }

    <!-- 🔷 Empty State -->
    @if (orders.length === 0 && !loading) {
      <div class="empty-state">
        <div class="empty-icon">
          <svg cIcon name="cil-inbox" class="empty-svg"></svg>
        </div>
        <h3>Aucune commande trouvée</h3>
        <p>Il n'y a aucune commande correspondant aux critères sélectionnés.</p>
      </div>
    }
  }
