import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { of, throwError } from 'rxjs';
import { EditCategoryComponent } from './edit-category.component';
import { CategoryService } from '../../../../../services/category.service';
import { Category } from '../../../../../models/category.model';

describe('EditCategoryComponent', () => {
  let component: EditCategoryComponent;
  let fixture: ComponentFixture<EditCategoryComponent>;
  let mockCategoryService: jasmine.SpyObj<CategoryService>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<EditCategoryComponent>>;

  const mockCategory: Category = {
    id: '1',
    name: 'Original Category',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  };

  const mockDialogData = {
    category: mockCategory
  };

  beforeEach(async () => {
    const categoryServiceSpy = jasmine.createSpyObj('CategoryService', ['updateCategory']);
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [EditCategoryComponent],
      providers: [
        { provide: CategoryService, useValue: categoryServiceSpy },
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EditCategoryComponent);
    component = fixture.componentInstance;
    mockCategoryService = TestBed.inject(CategoryService) as jasmine.SpyObj<CategoryService>;
    mockDialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<EditCategoryComponent>>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with category data', () => {
    expect(component.category).toEqual(mockCategory);
    expect(component.categoryData.name).toBe('Original Category');
    expect(component.isSubmitting()).toBeFalse();
    expect(component.error()).toBeNull();
    expect(component.success()).toBeNull();
  });

  it('should validate form correctly', () => {
    // Same name as original - should be invalid
    component.categoryData.name = 'Original Category';
    expect(component.isFormValid()).toBeFalse();

    // Different name - should be valid
    component.categoryData.name = 'Updated Category';
    expect(component.isFormValid()).toBeTrue();

    // Empty name - should be invalid
    component.categoryData.name = '';
    expect(component.isFormValid()).toBeFalse();

    // Whitespace only - should be invalid
    component.categoryData.name = '   ';
    expect(component.isFormValid()).toBeFalse();
  });

  it('should show error when submitting empty form', () => {
    component.categoryData.name = '';

    component.onSubmit();

    expect(component.error()).toBe('Le nom de la catégorie est requis');
    expect(mockCategoryService.updateCategory).not.toHaveBeenCalled();
  });

  it('should show error when submitting whitespace-only name', () => {
    component.categoryData.name = '   ';

    component.onSubmit();

    expect(component.error()).toBe('Le nom de la catégorie est requis');
    expect(mockCategoryService.updateCategory).not.toHaveBeenCalled();
  });

  it('should show error when no changes detected', () => {
    component.categoryData.name = 'Original Category';

    component.onSubmit();

    expect(component.error()).toBe('Aucune modification détectée');
    expect(mockCategoryService.updateCategory).not.toHaveBeenCalled();
  });

  it('should update category successfully', (done) => {
    const updatedCategory: Category = {
      ...mockCategory,
      name: 'Updated Category',
      updatedAt: '2024-01-02T00:00:00Z'
    };
    const mockResponse = {
      success: true,
      message: 'Category updated',
      category: updatedCategory
    };
    mockCategoryService.updateCategory.and.returnValue(of(mockResponse));
    component.categoryData.name = 'Updated Category';

    component.onSubmit();

    expect(component.isSubmitting()).toBeTrue();
    expect(mockCategoryService.updateCategory).toHaveBeenCalledWith('1', {
      name: 'Updated Category'
    });

    setTimeout(() => {
      expect(component.isSubmitting()).toBeFalse();
      expect(component.success()).toBe('Catégorie modifiée avec succès');
      expect(component.error()).toBeNull();
      done();
    }, 0);
  });

  it('should close modal after successful update', (done) => {
    const updatedCategory: Category = {
      ...mockCategory,
      name: 'Updated Category'
    };
    const mockResponse = {
      success: true,
      message: 'Category updated',
      category: updatedCategory
    };
    mockCategoryService.updateCategory.and.returnValue(of(mockResponse));
    component.categoryData.name = 'Updated Category';

    component.onSubmit();

    setTimeout(() => {
      expect(mockDialogRef.close).toHaveBeenCalledWith(updatedCategory);
      done();
    }, 1100); // Wait for the 1000ms delay + buffer
  });

  it('should handle update error', () => {
    const errorResponse = {
      error: { message: 'Category name already exists' },
      status: 409
    };
    mockCategoryService.updateCategory.and.returnValue(throwError(() => errorResponse));
    component.categoryData.name = 'Updated Category';

    component.onSubmit();

    expect(component.isSubmitting()).toBeFalse();
    expect(component.error()).toBe('Category name already exists');
    expect(component.success()).toBeNull();
  });

  it('should handle 409 conflict error specifically', () => {
    const errorResponse = {
      status: 409
    };
    mockCategoryService.updateCategory.and.returnValue(throwError(() => errorResponse));
    component.categoryData.name = 'Updated Category';

    component.onSubmit();

    expect(component.error()).toBe('Une catégorie avec ce nom existe déjà');
  });

  it('should handle generic error', () => {
    const errorResponse = {
      status: 500
    };
    mockCategoryService.updateCategory.and.returnValue(throwError(() => errorResponse));
    component.categoryData.name = 'Updated Category';

    component.onSubmit();

    expect(component.error()).toBe('Erreur lors de la modification de la catégorie');
  });

  it('should handle unsuccessful response', () => {
    const mockResponse = {
      success: false,
      message: 'Failed to update',
      category: mockCategory
    };
    mockCategoryService.updateCategory.and.returnValue(of(mockResponse));
    component.categoryData.name = 'Updated Category';

    component.onSubmit();

    expect(component.error()).toBe('Erreur lors de la modification de la catégorie');
    expect(component.success()).toBeNull();
  });

  it('should close modal on cancel', () => {
    component.onCancel();

    expect(mockDialogRef.close).toHaveBeenCalledWith();
  });

  it('should reset error and success messages on submit', () => {
    component.error.set('Previous error');
    component.success.set('Previous success');
    component.categoryData.name = '';

    component.onSubmit();

    expect(component.error()).toBe('Le nom de la catégorie est requis');
    expect(component.success()).toBeNull();
  });

  it('should trim whitespace when validating changes', () => {
    component.categoryData.name = '  Original Category  ';

    component.onSubmit();

    expect(component.error()).toBe('Aucune modification détectée');
  });
});
