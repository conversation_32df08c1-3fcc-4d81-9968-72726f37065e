import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog } from '@angular/material/dialog';
import { of, throwError } from 'rxjs';
import { CategoryComponent } from './category.component';
import { CategoryService } from '../../../../services/category.service';
import { Category } from '../../../../models/category.model';

describe('CategoryComponent', () => {
  let component: CategoryComponent;
  let fixture: ComponentFixture<CategoryComponent>;
  let mockCategoryService: jasmine.SpyObj<CategoryService>;
  let mockDialog: jasmine.SpyObj<MatDialog>;

  const mockCategories: Category[] = [
    {
      id: '1',
      name: 'Médicaments',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: 'Vitamines',
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-02T00:00:00Z'
    }
  ];

  beforeEach(async () => {
    const categoryServiceSpy = jasmine.createSpyObj('CategoryService', [
      'getAllCategories',
      'deleteCategory'
    ]);
    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);

    await TestBed.configureTestingModule({
      imports: [CategoryComponent],
      providers: [
        { provide: CategoryService, useValue: categoryServiceSpy },
        { provide: MatDialog, useValue: dialogSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CategoryComponent);
    component = fixture.componentInstance;
    mockCategoryService = TestBed.inject(CategoryService) as jasmine.SpyObj<CategoryService>;
    mockDialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load categories on init', () => {
    mockCategoryService.getAllCategories.and.returnValue(of({
      success: true,
      categories: mockCategories
    }));

    component.ngOnInit();

    expect(mockCategoryService.getAllCategories).toHaveBeenCalled();
    expect(component.allCategories).toEqual(mockCategories);
    expect(component.totalCategories).toBe(2);
  });

  it('should handle error when loading categories', () => {
    mockCategoryService.getAllCategories.and.returnValue(
      throwError(() => new Error('Network error'))
    );

    component.ngOnInit();

    expect(component.error).toBe('Erreur lors du chargement des catégories');
    expect(component.loading).toBeFalse();
  });

  it('should filter categories by search term', () => {
    component.allCategories = mockCategories;
    component.searchTerm = 'Méd';

    component.applyFilters();

    expect(component.filteredCategories.length).toBe(1);
    expect(component.filteredCategories[0].name).toBe('Médicaments');
  });

  it('should clear search and reset filters', () => {
    component.allCategories = mockCategories;
    component.searchTerm = 'test';
    component.filteredCategories = [];

    component.clearSearch();

    expect(component.searchTerm).toBe('');
    expect(component.filteredCategories).toEqual(mockCategories);
  });

  it('should toggle view mode', () => {
    expect(component.viewMode).toBe('table');

    component.toggleView();

    expect(component.viewMode).toBe('cards');

    component.toggleView();

    expect(component.viewMode).toBe('table');
  });

  it('should open add category modal', () => {
    const mockDialogRef = {
      afterClosed: () => of(null)
    };
    mockDialog.open.and.returnValue(mockDialogRef as any);

    component.addCategory();

    expect(mockDialog.open).toHaveBeenCalled();
  });

  it('should open edit category modal', () => {
    const mockDialogRef = {
      afterClosed: () => of(null)
    };
    mockDialog.open.and.returnValue(mockDialogRef as any);

    component.editCategory(mockCategories[0]);

    expect(mockDialog.open).toHaveBeenCalled();
    expect(component.showCategoryMenu()).toBeNull();
  });

  it('should delete category with confirmation', () => {
    spyOn(window, 'confirm').and.returnValue(true);
    mockCategoryService.deleteCategory.and.returnValue(of({
      success: true,
      message: 'Category deleted'
    }));
    spyOn(component, 'loadCategories');

    component.deleteCategory(mockCategories[0]);

    expect(window.confirm).toHaveBeenCalled();
    expect(mockCategoryService.deleteCategory).toHaveBeenCalledWith('1');
    expect(component.loadCategories).toHaveBeenCalled();
  });

  it('should not delete category when confirmation is cancelled', () => {
    spyOn(window, 'confirm').and.returnValue(false);

    component.deleteCategory(mockCategories[0]);

    expect(mockCategoryService.deleteCategory).not.toHaveBeenCalled();
  });

  it('should handle pagination correctly', () => {
    component.filteredCategories = mockCategories;
    component.itemsPerPage = 1;
    component.updatePagination();

    expect(component.totalPages).toBe(2);

    component.goToPage(2);
    expect(component.currentPage).toBe(2);

    component.previousPage();
    expect(component.currentPage).toBe(1);

    component.nextPage();
    expect(component.currentPage).toBe(2);
  });

  it('should format date correctly', () => {
    const dateString = '2024-01-01T00:00:00Z';
    const formatted = component.formatDate(dateString);

    expect(formatted).toBe('01/01/2024');
  });

  it('should return N/A for undefined date', () => {
    const formatted = component.formatDate(undefined);

    expect(formatted).toBe('N/A');
  });

  it('should get paginated categories correctly', () => {
    component.filteredCategories = mockCategories;
    component.currentPage = 1;
    component.itemsPerPage = 1;

    const paginated = component.getPaginatedCategories();

    expect(paginated.length).toBe(1);
    expect(paginated[0]).toEqual(mockCategories[0]);
  });

  it('should generate pagination info string', () => {
    component.currentPage = 1;
    component.itemsPerPage = 7;
    component.totalCategories = 10;

    const info = component.getPaginationInfo();

    expect(info).toBe('1-7 sur 10');
  });

  it('should toggle category menu', () => {
    const event = new Event('click');
    spyOn(event, 'preventDefault');
    spyOn(event, 'stopPropagation');

    component.toggleCategoryMenu('1', event);

    expect(event.preventDefault).toHaveBeenCalled();
    expect(event.stopPropagation).toHaveBeenCalled();
    expect(component.showCategoryMenu()).toBe('1');

    component.toggleCategoryMenu('1', event);
    expect(component.showCategoryMenu()).toBeNull();
  });

  it('should close menu on document click', () => {
    component.showCategoryMenu.set('1');
    const event = new Event('click');
    Object.defineProperty(event, 'target', {
      value: document.createElement('div')
    });

    component.onDocumentClick(event);

    expect(component.showCategoryMenu()).toBeNull();
  });
});
