// Edit Product Modal styles
.edit-product-modal {
  width: 950px;
  height: 550px;
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border: 2px solid #C7DAEC;
  border-radius: 10px;
  opacity: 1;
  padding: 25px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10003;
  font-family: 'Nautica Rounded', sans-serif;

  // Modal header
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-shrink: 0;

    .modal-title {
      font: normal normal 800 14px/17px 'Nautica Rounded', sans-serif;
      letter-spacing: 1.4px;
      color: #1061AC;
      text-align: left;
      margin: 0;
    }

    // Close Button (matching add prescription modal)
    .close-button {
      width: 24px;
      height: 24px;
      border: 2px solid #57B6B1;
      border-radius: 50%;
      opacity: 1;
      background: transparent;
      cursor: pointer;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background-color: #57B6B1;
      }

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 12px;
        height: 2px;
        background-color: #57B6B1;
        transition: background-color 0.2s ease;
      }

      &:hover::before,
      &:hover::after {
        background-color: white;
      }

      &::before {
        transform: rotate(45deg);
      }

      &::after {
        transform: rotate(-45deg);
      }
    }
  }

  // Modal content
  .modal-content {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;

    // Status toggle (positioned at top right)
    .status-toggle {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 12px;
      margin-bottom: 20px;

      .toggle-label {
        font-size: 14px;
        color: #163659;
        font-weight: 600;
      }

      .toggle-switch {
        width: 50px;
        height: 24px;
        background-color: #C7DAEC;
        border-radius: 12px;
        position: relative;
        cursor: pointer;
        transition: background-color 0.3s ease;

        .toggle-slider {
          width: 20px;
          height: 20px;
          background-color: #FFFFFF;
          border-radius: 50%;
          position: absolute;
          top: 2px;
          left: 2px;
          transition: transform 0.3s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &.active {
          background-color: #57B6B1;

          .toggle-slider {
            transform: translateX(26px);
          }
        }
      }
    }

    // Form content
    .form-content {
      display: flex;
      gap: 24px;
      flex: 1;

      // Image section (matching add product component)
      .image-section {
        flex: 0 0 200px;
        display: flex;
        flex-direction: column;
        gap: 16px;

        // Image Preview or Placeholder
        .image-placeholder {
          width: 200px;
          height: 200px;
          background-color: #E6EDF5;
          border: 2px dashed #6D9ECC;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 12px;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
          overflow: hidden;

          &:hover {
            border-color: #57B6B1;
            background-color: #ECEFF9;
          }

          &.has-image {
            border-style: solid;
            border-color: #6D9ECC;
            padding: 0;
            overflow: hidden;

            .image-container {
              position: relative;
              width: 100%;
              height: 100%;

              .preview-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              // Navigation Arrows
              .nav-arrow {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 32px;
                height: 32px;
                background-color: rgba(0, 0, 0, 0.6);
                border: none;
                border-radius: 50%;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
                z-index: 2;

                &:hover:not(:disabled) {
                  background-color: rgba(0, 0, 0, 0.8);
                  transform: translateY(-50%) scale(1.1);
                }

                &:disabled {
                  opacity: 0.4;
                  cursor: not-allowed;
                }

                .arrow-icon {
                  width: 16px;
                  height: 16px;
                }

                &.nav-arrow-left {
                  left: 8px;
                }

                &.nav-arrow-right {
                  right: 8px;
                }
              }

              // Image Counter
              .image-counter {
                position: absolute;
                bottom: 8px;
                left: 50%;
                transform: translateX(-50%);
                background-color: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
                z-index: 2;
              }

              .remove-image-btn {
                position: absolute;
                top: 8px;
                right: 8px;
                width: 28px;
                height: 28px;
                background-color: rgba(220, 53, 69, 0.9);
                border: none;
                border-radius: 50%;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.2s ease;
                z-index: 3;

                .remove-icon {
                  width: 14px;
                  height: 14px;
                }

                &:hover {
                  background-color: #dc3545;
                }
              }
            }
          }

          .upload-icon {
            width: 48px;
            height: 48px;
            color: #728A9B;
          }

          .upload-text {
            color: #728A9B;
            font-size: 16px;
            font-weight: 600;
          }
        }

        // Upload Button
        .upload-button {
          width: 200px;
          height: 40px;
          background-color: #1061AC;
          border: none;
          border-radius: 8px;
          color: white;
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          transition: all 0.2s ease;

          .button-icon {
            width: 16px;
            height: 16px;
          }

          &:hover {
            background-color: #0d4f8c;
            transform: translateY(-1px);
          }
        }
      }

      // Form section
      .form-section {
        flex: 1;

        .product-form {
          .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;

            &.full-width {
              .form-group {
                flex: 1;
              }
            }

            .form-group {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 6px;

              label {
                font-size: 14px;
                font-weight: 600;
                color: #163659;
              }

              .form-input, .form-select, .form-textarea {
                padding: 10px 12px;
                border: 1px solid #8AA3D5;
                border-radius: 6px;
                font-size: 14px;
                background-color: #FFFFFF;
                transition: border-color 0.2s ease;

                &::placeholder {
                  color: #728A9B;
                }

                &:focus {
                  outline: none;
                  border-color: #57B6B1;
                }

                &:invalid {
                  border-color: #dc3545;
                }
              }

              .form-textarea {
                resize: vertical;
                min-height: 80px;
                font-family: inherit;
              }

              // Custom dropdown styles (exact prescription style)
              .custom-dropdown {
                position: relative;
                min-width: 150px;

                .dropdown-toggle {
                  width: 100%;
                  padding: 0.5rem 0.75rem;
                  border: 1px solid #8AA3D5; // New border color
                  border-radius: 6px;
                  background: white;
                  font-size: 0.875rem;
                  color: #163659;
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  text-align: left;

                  &:focus {
                    outline: none;
                    border-color: #1061AC;
                    box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
                  }

                  &:hover {
                    border-color: #57B6B1;
                    background-color: #F5FFF9;
                  }

                  .dropdown-icon {
                    width: 12px;
                    height: 12px;
                    transition: transform 0.2s ease;

                    &.rotated {
                      transform: rotate(180deg);
                    }
                  }
                }

                // ✅ White background dropdown menu (exact copy from prescriptions)
                .dropdown-menu {
                  position: absolute;
                  top: 100%;
                  left: 0;
                  right: 0;
                  background: #FFFFFF !important;
                  border: 2px solid #C7DAEC !important;
                  border-radius: 8px;
                  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25) !important;
                  z-index: 99999 !important;
                  margin-top: 4px;
                  min-width: 200px !important;
                  max-height: 350px !important;
                  overflow-y: auto;
                  display: block !important;
                  visibility: visible !important;

                  .dropdown-item {
                    display: flex !important;
                    align-items: center;
                    padding: 0.75rem 1rem !important; // Reduced padding
                    cursor: pointer;
                    transition: all 0.2s ease;
                    border-bottom: 1px solid #e0e0e0 !important;
                    min-height: 40px !important; // Reduced height
                    background-color: #FFFFFF !important;

                    &:hover {
                      background-color: #F0F8FF !important;
                    }

                    &.selected {
                      background-color: #E8F4FD !important;
                      border-left: 3px solid #1061AC !important;
                      padding-left: 0.75rem !important;

                      .option-text {
                        color: #1061AC !important;
                        font-weight: 600 !important;
                      }
                    }

                    .option-text {
                      font-size: 0.875rem !important; // Smaller font
                      color: #163659 !important;
                      font-weight: 500 !important;
                      flex: 1;
                      line-height: 1.4;
                    }

                    &:first-child {
                      border-radius: 8px 8px 0 0;
                    }

                    &:last-child {
                      border-radius: 0 0 8px 8px;
                      border-bottom: none !important;
                    }
                  }
                }
              }

              .form-select {
                cursor: pointer;
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                background-position: right 12px center;
                background-repeat: no-repeat;
                background-size: 16px;
                padding-right: 36px;
                appearance: none;
              }
            }
          }
        }
      }
    }

    // Messages
    .message {
      margin-top: 16px;
      padding: 12px 16px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 14px;
      font-weight: 600;

      .message-icon {
        width: 18px;
        height: 18px;
        flex-shrink: 0;
      }

      &.error-message {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;

        .message-icon {
          color: #721c24;
        }
      }

      &.success-message {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;

        .message-icon {
          color: #155724;
        }
      }
    }
  }

  // Button Row (matching add prescription modal)
  .button-row {
    position: absolute;
    right: 25px;
    bottom: 25px;
    display: flex;
    gap: 15px;
    align-items: center;
  }

  // Cancel Button
  .cancel-button {
    width: 120px;
    height: 32px;
    background: transparent;
    border: 1px solid #163659;
    border-radius: 10px;
    color: #163659;
    cursor: pointer;
    font-weight: 600;
    font-size: 13px;
    transition: all 0.2s ease;
    font-family: 'Nautica Rounded', sans-serif;

    &:hover {
      background: #163659;
      color: white;
    }
  }

  // Submit Button
  .submit-button {
    width: 120px;
    height: 32px;
    background: #57B6B1;
    border: none;
    border-radius: 10px;
    opacity: 1;
    color: white;
    cursor: pointer;
    font-weight: 600;
    font-size: 13px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(87, 182, 177, 0.2);
    font-family: 'Nautica Rounded', sans-serif;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(87, 182, 177, 0.3);
      background: #4AA5A0;
    }

    &:disabled {
      background: #95A5A6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    &.loading {
      width: 160px; // Wider for loading text
    }

    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // Responsive design
  @media (max-width: 768px) {
    .modal-content {
      .form-content {
        flex-direction: column;

        .image-section {
          flex: none;
          align-self: center;
        }

        .form-section {
          .product-form {
            .form-row {
              flex-direction: column;
              gap: 12px;
            }
          }
        }
      }
    }

    .button-row {
      position: relative;
      right: auto;
      bottom: auto;
      margin-top: 20px;
      justify-content: center;

      .cancel-button,
      .submit-button {
        flex: 1;
      }
    }
  }
}