import { Component, OnInit, On<PERSON><PERSON>roy, inject, signal, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IconDirective } from '@coreui/icons-angular';
import { ProductService } from '../../../../services/product.service';
import { CategoryService } from '../../../../services/category.service';
import { Product, UpdateProductRequest } from '../../../../models/product.model';
import { Category } from '../../../../models/category.model';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-edit-product',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconDirective
  ],
  templateUrl: './edit-product.component.html',
  styleUrl: './edit-product.component.scss'
})
export class EditProductComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Original product data
  originalProduct: Product;

  // Form data
  productData: UpdateProductRequest = {
    name: '',
    description: '',
    quantity: 0,
    price: 0,
    categoryId: '',
    actif: true
  };

  // Categories
  categories: Category[] = [];

  // Multiple images upload
  selectedFiles: File[] = [];
  imagePreviews: string[] = [];
  currentImages: string[] = [];
  currentImageIndex = signal(0);
  uploadProgress = signal(0);
  isUploading = signal(false);

  // Form state
  isSubmitting = signal(false);
  error = signal<string | null>(null);
  success = signal<string | null>(null);

  // Dropdown states
  showCategoryDropdown = signal(false);

  // Inject dependencies using modern Angular approach
  private dialogRef = inject(MatDialogRef<EditProductComponent>);
  public data: { product: Product } = inject(MAT_DIALOG_DATA);
  private productService = inject(ProductService);
  private categoryService = inject(CategoryService);

  constructor() {
    this.originalProduct = this.data.product;
    this.initializeFormData();
  }

  ngOnInit() {
    this.loadCategories();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Initialize form data with product values
  initializeFormData() {
    this.productData = {
      name: this.originalProduct.name,
      description: this.originalProduct.description || '',
      quantity: this.originalProduct.quantity,
      price: this.originalProduct.price,
      categoryId: this.originalProduct.categoryId,
      actif: this.originalProduct.actif
    };

    // Set current images URLs
    if (this.originalProduct.storagePath && this.originalProduct.storagePath.length > 0) {
      this.currentImages = this.originalProduct.storagePath.map(path =>
        `${environment.fileBaseUrl}${path}`
      );
      this.currentImageIndex.set(0);
    }
  }

  // Load categories for dropdown
  loadCategories() {
    this.categoryService.getAllCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.categories = response.categories || [];
          }
        },
        error: (error) => {
          console.error('Error loading categories:', error);
          this.error.set('Erreur lors du chargement des catégories');
        }
      });
  }

  // Image upload methods
  onImageSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const files = Array.from(input.files);

      for (const file of files) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
          this.error.set('Veuillez sélectionner uniquement des fichiers image valides');
          continue;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          this.error.set('La taille des fichiers ne doit pas dépasser 5MB chacun');
          continue;
        }

        this.selectedFiles.push(file);
        this.error.set(null);

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
          this.imagePreviews.push(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      }
    }
  }

  removeCurrentImage() {
    const currentIndex = this.currentImageIndex();
    const totalImages = this.getTotalImages();

    if (currentIndex < this.imagePreviews.length) {
      // Remove from new images (previews)
      this.imagePreviews.splice(currentIndex, 1);
      this.selectedFiles.splice(currentIndex, 1);
    } else {
      // Remove from existing images
      const existingIndex = currentIndex - this.imagePreviews.length;
      this.currentImages.splice(existingIndex, 1);
    }

    // Adjust current index if necessary
    if (currentIndex >= totalImages - 1 && currentIndex > 0) {
      this.currentImageIndex.set(currentIndex - 1);
    }

    // Reset file input if no more images
    if (this.getTotalImages() === 0) {
      const fileInput = document.getElementById('imageInput') as HTMLInputElement;
      if (fileInput) {
        fileInput.value = '';
      }
    }
  }

  // Navigation methods
  previousImage() {
    const currentIndex = this.currentImageIndex();
    if (currentIndex > 0) {
      this.currentImageIndex.set(currentIndex - 1);
    }
  }

  nextImage() {
    const currentIndex = this.currentImageIndex();
    const totalImages = this.getTotalImages();
    if (currentIndex < totalImages - 1) {
      this.currentImageIndex.set(currentIndex + 1);
    }
  }

  // Form submission
  onSubmit() {
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting.set(true);
    this.error.set(null);
    this.success.set(null);

    if (this.selectedFiles.length > 0) {
      // Upload new images first, then update product
      this.uploadMultipleImages();
    } else {
      // Update product with existing images only
      const updateData = {
        ...this.productData,
        storagePath: this.currentImages.map(url => url.replace(environment.fileBaseUrl, ''))
      };
      this.updateProduct(updateData);
    }
  }

  private uploadMultipleImages() {
    const uploadPromises = this.selectedFiles.map(file =>
      this.productService.uploadProductImage(file).toPromise()
    );

    Promise.all(uploadPromises)
      .then(responses => {
        const newImagePaths = responses
          .filter(response => response?.filePath)
          .map(response => response!.filePath as string);

        // Combine existing images with new ones
        const existingPaths = this.currentImages.map(url => url.replace(environment.fileBaseUrl, ''));
        const allImagePaths = [...existingPaths, ...newImagePaths];

        const updateData = {
          ...this.productData,
          storagePath: allImagePaths
        };
        this.updateProduct(updateData);
      })
      .catch(error => {
        console.error('❌ Error uploading images:', error);
        this.error.set('Erreur lors du téléchargement des images');
        this.isSubmitting.set(false);
      });
  }

  private updateProduct(updateData: UpdateProductRequest) {
    this.productService.updateProduct(this.originalProduct.id, updateData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('✅ Product updated:', response);
          this.success.set('Produit mis à jour avec succès!');
          this.isSubmitting.set(false);

          // Close modal after 1.5 seconds
          setTimeout(() => {
            this.dialogRef.close(response.product);
          }, 1500);
        },
        error: (error) => {
          console.error('❌ Error updating product:', error);
          this.error.set('Erreur lors de la mise à jour du produit');
          this.isSubmitting.set(false);
        }
      });
  }

  // Form validation
  validateForm(): boolean {
    if (!this.productData.name?.trim()) {
      this.error.set('Le nom du produit est requis');
      return false;
    }

    if (!this.productData.categoryId) {
      this.error.set('La catégorie est requise');
      return false;
    }

    if (this.productData.quantity !== undefined && this.productData.quantity < 0) {
      this.error.set('La quantité ne peut pas être négative');
      return false;
    }

    if (this.productData.price !== undefined && this.productData.price <= 0) {
      this.error.set('Le prix doit être supérieur à 0');
      return false;
    }

    return true;
  }

  // Modal actions
  onCancel() {
    this.dialogRef.close();
  }

  // Toggle active status
  toggleActiveStatus() {
    this.productData.actif = !this.productData.actif;
  }

  // Helper methods
  getTotalImages(): number {
    return this.imagePreviews.length + this.currentImages.length;
  }

  getCurrentImageUrl(): string | null {
    const currentIndex = this.currentImageIndex();
    const totalPreviews = this.imagePreviews.length;

    if (currentIndex < totalPreviews) {
      return this.imagePreviews[currentIndex];
    } else {
      const existingIndex = currentIndex - totalPreviews;
      return this.currentImages[existingIndex] || null;
    }
  }

  hasImages(): boolean {
    return this.getTotalImages() > 0;
  }

  canNavigatePrevious(): boolean {
    return this.currentImageIndex() > 0;
  }

  canNavigateNext(): boolean {
    return this.currentImageIndex() < this.getTotalImages() - 1;
  }

  getImageCounter(): string {
    const current = this.currentImageIndex() + 1;
    const total = this.getTotalImages();
    return `${current}/${total}`;
  }

  getCategoryName(categoryId: string): string {
    const category = this.categories.find(c => c.id === categoryId);
    return category ? category.name : 'Catégorie inconnue';
  }

  // Dropdown methods (using prescription pattern)
  toggleCategoryDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showCategoryDropdown.set(!this.showCategoryDropdown());
  }

  // Close dropdowns when clicking outside (prescription pattern)
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.custom-dropdown')) {
      this.showCategoryDropdown.set(false);
    }
  }

  selectCategory(categoryId: string) {
    this.productData.categoryId = categoryId;
    this.showCategoryDropdown.set(false);
  }

  getSelectedCategoryText(): string {
    if (!this.productData.categoryId) return 'Sélectionner une catégorie';
    const category = this.categories.find(c => c.id === this.productData.categoryId);
    return category ? category.name : 'Sélectionner une catégorie';
  }
}
