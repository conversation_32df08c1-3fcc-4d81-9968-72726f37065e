import { Component, OnInit, OnD<PERSON>roy, signal, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { ButtonDirective } from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { MatDialog } from '@angular/material/dialog';
import { ProductService } from '../../../services/product.service';
import { CategoryService } from '../../../services/category.service';
import { Product, ProductFilters, StockStatus } from '../../../models/product.model';
import { Category } from '../../../models/category.model';
import { environment } from '../../../../environments/environment';
import { EditProductComponent } from './edit-product/edit-product.component';
import { ProductDetailsComponent } from './product-details/product-details.component';

@Component({
  selector: 'app-stock',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonDirective,
    IconDirective,
    EditProductComponent
  ],
  templateUrl: './stock.component.html',
  styleUrls: ['./stock.component.scss']
})
export class StockComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Make Math available in template
  Math = Math;

  viewMode: 'table' | 'cards' = 'table';
  activeCardMenu: string | null = null;
  products: Product[] = [];
  categories: Category[] = [];
  loading = false;
  error: string | null = null;

  // Filter properties - using signals for reactivity
  showFilter = signal(false);
  selectedStockStatus = signal('');
  selectedActiveStatus = signal('');
  searchQuery = signal('');
  showStockStatusDropdown = signal(false);
  showActiveStatusDropdown = signal(false);

  // Product menu state
  showProductMenu = signal<string | null>(null);

  // Image navigation for products with multiple images
  productImageIndexes: { [productId: string]: number } = {};

  // Filters
  filters: ProductFilters = {};

  // Pagination
  totalProducts = 0;
  currentPage = 1;
  itemsPerPage = 3; // Initialize to 3 as requested
  totalPages = 0;
  availablePageSizes = [3, 5, 7, 9];
  allProducts: Product[] = [];
  paginatedProducts: Product[] = [];

  // Stock status options
  stockStatusOptions = [
    { value: '', label: 'Tous les stocks' },
    { value: StockStatus.IN_STOCK, label: 'En stock' },
    { value: StockStatus.OUT_OF_STOCK, label: 'Rupture de stock' }
  ];

  // Active status options
  activeStatusOptions = [
    { value: '', label: 'Tous les états' },
    { value: 'true', label: 'Actif' },
    { value: 'false', label: 'Inactif' }
  ];

  constructor(
    private router: Router,
    private productService: ProductService,
    private categoryService: CategoryService,
    private dialog: MatDialog
  ) {
    // Initialize arrays to prevent null errors
    this.allProducts = [];
    this.paginatedProducts = [];
  }

  ngOnInit() {
    console.log('StockComponent initialized');
    // Initialize pagination properly
    this.updatePagination();
    this.loadProducts();
    this.loadCategories();
    this.subscribeToServiceState();
    this.subscribeToServiceEvents();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Load products from service
  loadProducts() {
    this.loading = true;
    this.error = null;

    this.productService.getProductsForPharmacy(this.filters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('✅ Products loaded:', response);
          if (response.success) {
            this.allProducts = response.products || [];
            this.totalProducts = response.total || 0;
            // Reset image navigation indexes when products are loaded
            this.resetImageIndexes();
            this.updatePagination();
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('❌ Error loading products:', error);
          this.error = 'Erreur lors du chargement des produits';
          this.loading = false;
        }
      });
  }

  // Load categories from service
  loadCategories() {
    this.categoryService.getAllCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('✅ Categories loaded:', response);
          if (response.success) {
            this.categories = response.categories || [];
          }
        },
        error: (error) => {
          console.error('❌ Error loading categories:', error);
        }
      });
  }

  // Subscribe to service state changes
  subscribeToServiceState() {
    this.productService.products$
      .pipe(takeUntil(this.destroy$))
      .subscribe(products => {
        this.allProducts = products;
        this.updatePagination();
      });

    this.productService.loading$
      .pipe(takeUntil(this.destroy$))
      .subscribe(loading => {
        this.loading = loading;
      });

    this.productService.error$
      .pipe(takeUntil(this.destroy$))
      .subscribe(error => {
        this.error = error;
      });
  }

  // Subscribe to service events
  subscribeToServiceEvents() {
    this.productService.listen()
      .pipe(takeUntil(this.destroy$))
      .subscribe(event => {
        console.log('🔔 Product service event received:', event);
        if (event === 'ProductCreated' || event === 'ProductUpdated' || event === 'ProductDeleted') {
          // Refresh the current view
          this.loadProducts();
        }
      });
  }

  // Pagination methods
  updatePagination() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedProducts = this.allProducts.slice(startIndex, endIndex);
    this.products = this.paginatedProducts;
    this.totalPages = Math.ceil(this.totalProducts / this.itemsPerPage);
  }

  getTotalPages(): number {
    return Math.ceil(this.totalProducts / this.itemsPerPage);
  }

  getTotalCount(): number {
    return this.totalProducts;
  }

  getEndItemNumber(): number {
    return Math.min(this.currentPage * this.itemsPerPage, this.totalProducts);
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.getTotalPages()) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  goToFirstPage() {
    this.goToPage(1);
  }

  goToPreviousPage() {
    this.goToPage(this.currentPage - 1);
  }

  goToNextPage() {
    this.goToPage(this.currentPage + 1);
  }

  goToLastPage() {
    this.goToPage(this.getTotalPages());
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const maxVisiblePages = 5;
    const pages: number[] = [];

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  }

  onPageSizeChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    this.itemsPerPage = parseInt(target.value);
    this.currentPage = 1;
    this.updatePagination();
  }

  // Filter methods
  toggleFilter() {
    this.showFilter.set(!this.showFilter());
  }

  toggleView() {
    this.viewMode = this.viewMode === 'table' ? 'cards' : 'table';
  }

  // Custom dropdown methods (using prescription pattern)
  toggleStockStatusDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showStockStatusDropdown.set(!this.showStockStatusDropdown());
    this.showActiveStatusDropdown.set(false); // Close other dropdown
  }

  toggleActiveStatusDropdown(event?: Event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.showActiveStatusDropdown.set(!this.showActiveStatusDropdown());
    this.showStockStatusDropdown.set(false); // Close other dropdown
  }

  // Close dropdowns when clicking outside (prescription pattern)
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.custom-dropdown')) {
      this.showStockStatusDropdown.set(false);
      this.showActiveStatusDropdown.set(false);
    }
    if (!target.closest('.menu-container')) {
      this.showProductMenu.set(null);
    }
  }

  // Product menu methods
  toggleProductMenu(productId: string, event: Event) {
    event.preventDefault();
    event.stopPropagation();

    if (this.showProductMenu() === productId) {
      this.showProductMenu.set(null);
    } else {
      this.showProductMenu.set(productId);
    }
  }

  selectStockStatus(status: string) {
    this.selectedStockStatus.set(status);
    this.showStockStatusDropdown.set(false);
    // Reset to first page when changing filters
    this.currentPage = 1;
    this.applyFilters();
  }

  selectActiveStatus(status: string) {
    this.selectedActiveStatus.set(status);
    this.showActiveStatusDropdown.set(false);
    // Reset to first page when changing filters
    this.currentPage = 1;
    this.applyFilters();
  }

  onSearchChange() {
    this.applyFilters();
  }

  applyFilters() {
    const stockStatusValue = this.selectedStockStatus();
    const activeStatusValue = this.selectedActiveStatus();

    this.filters = {
      search: this.searchQuery() || undefined,
      stockStatus: stockStatusValue ? stockStatusValue as StockStatus : undefined,
      actif: activeStatusValue ? activeStatusValue === 'true' : undefined
    };

    this.currentPage = 1; // Reset to first page when filtering
    this.loadProducts();
  }

  clearFilters() {
    this.selectedStockStatus.set('');
    this.selectedActiveStatus.set('');
    this.searchQuery.set('');
    this.filters = {};
    this.currentPage = 1;
    this.loadProducts();
  }

  // Action methods
  navigateToAddProduct() {
    this.router.navigate(['/orders/stock/add-product']);
  }

  editProduct(product: Product) {
    console.log('Modifier le produit:', product);
    this.showProductMenu.set(null); // Close menu

    const dialogRef = this.dialog.open(EditProductComponent, {
      width: '950px',
      height: '550px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      data: { product: product },
      disableClose: false,
      panelClass: 'custom-modal',
      position: {
        left: 'calc(256px + (100vw - 256px - 950px) / 2)'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Product updated:', result);
        // Refresh the product list to show updated data
        this.loadProducts();
      }
    });
  }

  deleteProduct(product: Product) {
    console.log('Supprimer le produit:', product);
    this.showProductMenu.set(null); // Close menu

    // Show confirmation dialog
    if (confirm(`Êtes-vous sûr de vouloir supprimer le produit "${product.name}" ?`)) {
      this.productService.deleteProduct(product.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            console.log('✅ Produit supprimé:', response);
            // The service will handle the state update via events
          },
          error: (error) => {
            console.error('❌ Erreur lors de la suppression du produit:', error);
            this.error = 'Erreur lors de la suppression du produit';
          }
        });
    }
  }

  viewProduct(product: Product) {
    // Close any open card menu
    this.activeCardMenu = null;

    // Open product details modal
    const dialogRef = this.dialog.open(ProductDetailsComponent, {
      width: '950px',
      height: '550px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      panelClass: 'custom-modal',
      data: { product: product },
      disableClose: false,
      position: {
        left: 'calc(256px + (100vw - 256px - 950px) / 2)'
      }
    });

    dialogRef.afterClosed().subscribe(() => {
      // Handle modal close if needed
      console.log('Product details modal closed');
    });
  }



  // Card menu methods
  toggleCardMenu(productId: string, event: Event) {
    event.stopPropagation();
    this.activeCardMenu = this.activeCardMenu === productId ? null : productId;
  }

  closeCardMenu() {
    this.activeCardMenu = null;
  }

  // Helper methods
  getProductImageUrl(product: Product): string {
    if (product.storagePath && product.storagePath.length > 0) {
      const currentIndex = this.productImageIndexes[product.id] || 0;
      const imageIndex = Math.min(currentIndex, product.storagePath.length - 1);
      return `${environment.fileBaseUrl}${product.storagePath[imageIndex]}`;
    }
    return 'assets/images/product-placeholder.svg'; // Default placeholder
  }

  // Image navigation methods
  hasMultipleImages(product: Product): boolean {
    return !!(product.storagePath && product.storagePath.length > 1);
  }

  getCurrentImageIndex(product: Product): number {
    return this.productImageIndexes[product.id] || 0;
  }

  canNavigatePrevious(product: Product): boolean {
    return this.getCurrentImageIndex(product) > 0;
  }

  canNavigateNext(product: Product): boolean {
    const currentIndex = this.getCurrentImageIndex(product);
    return !!(product.storagePath && currentIndex < product.storagePath.length - 1);
  }

  previousImage(product: Product, event?: Event): void {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    if (this.canNavigatePrevious(product)) {
      const currentIndex = this.getCurrentImageIndex(product);
      this.productImageIndexes[product.id] = currentIndex - 1;
    }
  }

  nextImage(product: Product, event?: Event): void {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    if (this.canNavigateNext(product)) {
      const currentIndex = this.getCurrentImageIndex(product);
      this.productImageIndexes[product.id] = currentIndex + 1;
    }
  }

  getImageCounter(product: Product): string {
    if (!this.hasMultipleImages(product)) return '';
    const current = this.getCurrentImageIndex(product) + 1;
    const total = product.storagePath?.length || 0;
    return `${current}/${total}`;
  }

  // Reset all image navigation indexes
  resetImageIndexes(): void {
    this.productImageIndexes = {};
  }

  getStockStatusText(status: StockStatus | undefined): string {
    if (!status) return 'Inconnu';
    switch (status) {
      case StockStatus.IN_STOCK:
        return 'En stock';
      case StockStatus.OUT_OF_STOCK:
        return 'Rupture de stock';
      default:
        return 'Inconnu';
    }
  }

  getStockStatusBadgeClass(status: StockStatus | undefined): string {
    if (!status) return 'badge-unknown';
    return this.productService.getStockStatusBadgeClass(status);
  }

  getActiveStatusText(actif: boolean): string {
    return actif ? 'Actif' : 'Inactif';
  }

  getActiveStatusBadgeClass(actif: boolean): string {
    return this.productService.getActiveStatusBadgeClass(actif);
  }

  getCategoryName(categoryId: string): string {
    const category = this.categories.find(c => c.id === categoryId);
    return category ? category.name : 'Catégorie inconnue';
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  }

  // Helper methods for template
  getSelectedStockStatusLabel(): string {
    const selectedValue = this.selectedStockStatus();
    if (!selectedValue) return 'Stock';
    const option = this.stockStatusOptions.find(opt => opt.value === selectedValue);
    return option ? option.label : 'Stock';
  }

  getSelectedActiveStatusLabel(): string {
    const selectedValue = this.selectedActiveStatus();
    if (!selectedValue) return 'État';
    const option = this.activeStatusOptions.find(opt => opt.value === selectedValue);
    return option ? option.label : 'État';
  }

  // Create array for pagination
  getPaginationArray(): number[] {
    const totalPages = this.getTotalPages();
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  // Handle image loading errors
  onImageError(event: Event) {
    const target = event.target as HTMLImageElement;
    if (target) {
      target.src = 'assets/images/product-placeholder.svg';
    }
  }
}
