import { Component, OnInit, On<PERSON><PERSON>roy, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IconDirective } from '@coreui/icons-angular';
import { ProductService } from '../../../../services/product.service';
import { CategoryService } from '../../../../services/category.service';
import { Product } from '../../../../models/product.model';
import { Category } from '../../../../models/category.model';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-product-details',
  standalone: true,
  imports: [
    CommonModule,
    IconDirective
  ],
  templateUrl: './product-details.component.html',
  styleUrl: './product-details.component.scss'
})
export class ProductDetailsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Product data
  product: Product;
  category: Category | null = null;

  // Image navigation
  currentImages: string[] = [];
  currentImageIndex = signal(0);

  // Loading states
  loading = false;
  error: string | null = null;

  constructor(
    private dialogRef: MatDialogRef<ProductDetailsComponent>,
    private productService: ProductService,
    private categoryService: CategoryService
  ) {
    const data = inject(MAT_DIALOG_DATA);
    this.product = data.product;
  }

  ngOnInit() {
    this.loadProductDetails();
    this.loadCategory();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadProductDetails() {
    // Set up current images from product
    if (this.product.storagePath && this.product.storagePath.length > 0) {
      this.currentImages = this.product.storagePath.map(path =>
        `${environment.fileBaseUrl}${path}`
      );
    }
  }

  loadCategory() {
    if (this.product.categoryId) {
      this.categoryService.getCategoryById(this.product.categoryId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.success) {
              this.category = response.category;
            }
          },
          error: (error) => {
            console.error('Error loading category:', error);
          }
        });
    }
  }

  // Image navigation methods
  previousImage() {
    const currentIndex = this.currentImageIndex();
    if (currentIndex > 0) {
      this.currentImageIndex.set(currentIndex - 1);
    }
  }

  nextImage() {
    const currentIndex = this.currentImageIndex();
    const totalImages = this.getTotalImages();
    if (currentIndex < totalImages - 1) {
      this.currentImageIndex.set(currentIndex + 1);
    }
  }

  getTotalImages(): number {
    return this.currentImages.length;
  }

  getCurrentImageUrl(): string | null {
    const currentIndex = this.currentImageIndex();
    return this.currentImages[currentIndex] || null;
  }

  hasImages(): boolean {
    return this.currentImages.length > 0;
  }

  canNavigatePrevious(): boolean {
    return this.currentImageIndex() > 0;
  }

  canNavigateNext(): boolean {
    return this.currentImageIndex() < this.currentImages.length - 1;
  }

  getImageCounter(): string {
    const current = this.currentImageIndex() + 1;
    const total = this.currentImages.length;
    return `${current}/${total}`;
  }

  // Helper methods
  getActiveStatusText(): string {
    return this.product.actif ? 'Actif' : 'Inactif';
  }

  getCategoryName(): string {
    return this.category?.name || 'Catégorie non trouvée';
  }

  // Modal actions
  onClose() {
    this.dialogRef.close();
  }
}
