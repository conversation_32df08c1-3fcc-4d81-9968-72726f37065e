// Stock component styles (matching prescription design exactly)
.prescriptions-container {
  padding: 20px;
  background-color: #FFFFFF;
  min-height: 100vh;
  font-family: 'Nautica Rounded', sans-serif;

  .prescriptions-header {
    margin-bottom: 20px;

    .title-and-icons {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #163659;
        margin: 0;
      }

      .header-icons {
        display: flex;
        gap: 10px;

        .icon-button {
          width: 40px;
          height: 40px;
          border: none;
          border-radius: 8px;
          background-color: #FFFFFF;
          border: 1px solid #C7DAEC;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;

          .icon {
            width: 20px;
            height: 20px;
            color: #163659;
          }

          &:hover {
            background-color: #C7DAEC;
          }
        }
      }
    }

    .filter-container {
      background-color: #ECEFF9;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 20px;

      .filter-row {
        display: flex;
        gap: 20px;
        align-items: flex-end;

        .filter-group {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .filter-label {
            font-size: 14px;
            font-weight: 600;
            color: #163659;
          }

            .custom-dropdown {
              position: relative;
              min-width: 150px;

              .dropdown-toggle {
                width: 100%;
                padding: 0.5rem 0.75rem;
                border: 1px solid #C7DAEC;
                border-radius: 6px;
                background: white;
                font-size: 0.875rem;
                color: #163659;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
                text-align: left;

                &:focus {
                  outline: none;
                  border-color: #1061AC;
                  box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
                }

                &:hover {
                  border-color: #57B6B1;
                  background-color: #F5FFF9;
                }
              }

              // ✅ White background dropdown menu (exact copy from prescriptions)
              .dropdown-menu {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: #FFFFFF !important;
                border: 2px solid #C7DAEC !important;
                border-radius: 8px;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25) !important;
                z-index: 99999 !important;
                margin-top: 4px;
                min-width: 200px !important;
                max-height: 350px !important;
                overflow-y: auto;
                display: block !important;
                visibility: visible !important;

                .dropdown-item {
                  display: flex !important;
                  align-items: center;
                  padding: 1rem 1.25rem !important;
                  cursor: pointer;
                  transition: all 0.2s ease;
                  gap: 0.75rem;
                  border-bottom: 1px solid #e0e0e0 !important;
                  min-height: 48px !important;
                  background-color: #FFFFFF !important;

                  &:hover {
                    background-color: #F0F8FF !important;
                    border-left: 3px solid #1061AC !important;
                    padding-left: 1rem !important;
                  }

                  // ✅ Custom rectangular checkbox - smaller and wider than tall
                  .checkbox-square {
                    width: 18px !important;
                    height: 14px !important;
                    border: 2px solid #1061AC !important;
                    border-radius: 3px;
                    background: white !important;
                    position: relative;
                    flex-shrink: 0;
                    transition: all 0.2s ease;
                    display: inline-block !important;
                    margin-right: 0.75rem;

                    // ✅ Checkmark when selected - adjusted for smaller rectangular checkbox
                    &::after {
                      content: '';
                      position: absolute;
                      top: 1px;
                      left: 4px;
                      width: 4px;
                      height: 8px;
                      border: solid white;
                      border-width: 0 2px 2px 0;
                      transform: rotate(45deg);
                      opacity: 0;
                      transition: opacity 0.2s ease;
                    }

                    // ✅ When checked - Make it very visible
                    &.checked {
                      background-color: #1061AC !important;
                      border-color: #1061AC !important;
                      box-shadow: 0 0 0 1px #1061AC !important;

                      &::after {
                        opacity: 1 !important;
                      }
                    }
                  }

                  .option-text {
                    font-size: 1rem !important;
                    color: #000000 !important;
                    font-weight: 500 !important;
                    flex: 1;
                    line-height: 1.4;
                  }

                  &:first-child {
                    border-radius: 8px 8px 0 0;
                  }

                  &:last-child {
                    border-radius: 0 0 8px 8px;
                    border-bottom: none !important;
                  }
                }
              }
            }
          }
        }
      }
    }

    .action-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .primary-button {
        background-color: #1061AC;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 20px;
        font-weight: 800;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;

        .button-icon {
          width: 16px;
          height: 16px;
        }

        &:hover {
          background-color: #0d4f8c;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(16, 97, 172, 0.3);
        }
      }

      .search-container {
        position: relative;
        width: 300px;

        .search-input {
          width: 100%;
          padding: 12px 40px 12px 16px;
          border: 1px solid #C7DAEC;
          border-radius: 8px;
          font-size: 14px;
          background-color: #FFFFFF;

          &::placeholder {
            color: #728A9B;
          }

          &:focus {
            outline: none;
            border-color: #57B6B1;
          }
        }

        .search-icon {
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          width: 16px;
          height: 16px;
          color: #728A9B;
        }
      }
    }
  }


// Table styles (matching prescriptions exactly)
.table-container {
  background: white;
  border-radius: 10px;
  border: 1px solid #C7DAEC;
  overflow: hidden;
  margin-bottom: 20px;
}

.prescriptions-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;

  thead {
    background-color: #F8F9FA;
    
    th {
      padding: 16px 12px;
      text-align: left;
      font-weight: 600;
      color: #163659;
      border-bottom: 1px solid #C7DAEC;
      font-size: 14px;
    }
  }

  tbody {
    tr {
      border-bottom: 1px solid #E8EDF5;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #F5F7FA;
      }

      &:last-child {
        border-bottom: none;
      }
    }

    td {
      padding: 16px 12px;
      vertical-align: middle;
      color: #163659;

      &.product-image-cell {
        width: 80px;

        .table-image-container {
          position: relative;
          width: 60px;
          height: 60px;

          .product-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid #E8EDF5;
          }

          // Table navigation arrows
          .table-nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            background-color: rgba(0, 0, 0, 0.6);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            z-index: 2;
            opacity: 0;

            &:hover:not(:disabled) {
              background-color: rgba(0, 0, 0, 0.8);
              transform: translateY(-50%) scale(1.1);
            }

            &:disabled {
              opacity: 0.3;
              cursor: not-allowed;
            }

            .arrow-icon {
              width: 10px;
              height: 10px;
            }

            &.table-nav-arrow-left {
              left: 2px;
            }

            &.table-nav-arrow-right {
              right: 2px;
            }
          }

          // Table image counter
          .table-image-counter {
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 1px 4px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
            z-index: 2;
            opacity: 0;
          }

          // Show arrows and counter on hover
          &:hover {
            .table-nav-arrow,
            .table-image-counter {
              opacity: 1;
            }
          }
        }
      }

      .product-name-cell {
        .product-name {
          font-size: 14px;
          font-weight: 600;
          color: #163659;
          margin-bottom: 2px;
        }

        .product-category {
          font-size: 12px;
          color: #728A9B;
          font-style: italic;
        }
      }

      .price-cell {
        .unit-price {
          font-size: 14px;
          font-weight: 600;
          color: #163659;
        }

        .discount {
          font-size: 12px;
          color: #F80D38;
          font-weight: 500;
          margin-top: 2px;
        }
      }

      &.actions-cell {
        width: 120px;
        
        button {
          background: none;
          border: none;
          padding: 8px;
          margin: 0 2px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          
          &:hover {
            background-color: #F5F7FA;
          }
          
          svg {
            width: 16px;
            height: 16px;
            color: #728A9B;
          }
          
          .eye-icon {
            width: 16px;
            height: 16px;
          }
        }
      }

      .badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
      }

      .quantity {
        font-size: 12px;
        color: #728A9B;
        margin-top: 2px;
      }
    }
  }
}

/* Cards View (compact vertical layout) */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
  padding: 0;
}

.product-card {
  background: #FFFFFF;
  border: 1px solid #C7DAEC;
  border-radius: 10px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  position: relative;
  height: auto;
  min-height: 280px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  }

  // Out of stock ribbon
  .out-of-stock-ribbon {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;

    .ribbon-icon {
      width: 40px;
      height: 40px;
      filter: hue-rotate(0deg) saturate(1.5) brightness(0.8);
    }
  }

  // Product image on top
  .product-image-container {
    position: relative;
    width: 100%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    background-color: #F8F9FA;
    border-radius: 8px;
    overflow: hidden;

    .product-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    // Card navigation arrows
    .card-nav-arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 28px;
      height: 28px;
      background-color: rgba(0, 0, 0, 0.6);
      border: none;
      border-radius: 50%;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      z-index: 2;
      opacity: 0;

      &:hover:not(:disabled) {
        background-color: rgba(0, 0, 0, 0.8);
        transform: translateY(-50%) scale(1.1);
      }

      &:disabled {
        opacity: 0.4;
        cursor: not-allowed;
      }

      .arrow-icon {
        width: 14px;
        height: 14px;
      }

      &.card-nav-arrow-left {
        left: 8px;
      }

      &.card-nav-arrow-right {
        right: 8px;
      }
    }

    // Card image counter
    .card-image-counter {
      position: absolute;
      bottom: 8px;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 3px 6px;
      border-radius: 10px;
      font-size: 11px;
      font-weight: 500;
      z-index: 2;
      opacity: 0;
    }

    // Show arrows and counter on hover
    &:hover {
      .card-nav-arrow,
      .card-image-counter {
        opacity: 1;
      }
    }
  }

  // Product name with toggle switch and menu
  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;

    .product-name {
      font-size: 0.875rem;
      font-weight: 600;
      color: #163659;
      margin: 0;
      line-height: 1.2;
      flex: 1;
      margin-right: 0.5rem;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      // Toggle switch styling
      .toggle-switch {
        width: 32px;
        height: 18px;
        background-color: #E0E0E0;
        border-radius: 9px;
        position: relative;
        cursor: pointer;
        transition: background-color 0.3s ease;

        .toggle-slider {
          width: 14px;
          height: 14px;
          background-color: white;
          border-radius: 50%;
          position: absolute;
          top: 2px;
          left: 2px;
          transition: transform 0.3s ease;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        &.active {
          background-color: #57B6B1;

          .toggle-slider {
            transform: translateX(14px);
          }
        }

        &.inactive {
          background-color: #F80D38;
        }
      }

      // 3-dot menu styling (matching prescription design)
      .menu-container {
        position: relative;

        .menu-button {
          background: none;
          border: none;
          padding: 0.25rem;
          cursor: pointer;
          border-radius: 4px;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: #F5F7FA;
          }

          svg {
            display: block;
          }
        }

        .menu-dropdown {
          position: absolute;
          top: 100%;
          right: 0;
          background: white;
          border: 1px solid #C7DAEC;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          z-index: 1000;
          min-width: 140px;
          margin-top: 4px;

          .menu-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: none;
            border: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            transition: background-color 0.2s ease;
            font-size: 0.875rem;
            color: #163659;

            &:hover {
              background-color: #F5F7FA;
            }

            &:first-child {
              border-radius: 8px 8px 0 0;
            }

            &:last-child {
              border-radius: 0 0 8px 8px;
            }

            &.delete {
              color: #F80D38;

              &:hover {
                background-color: #FFF5F5;
              }
            }

            .menu-icon {
              width: 14px;
              height: 14px;
              flex-shrink: 0;
            }

            span {
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  // Product type/category
  .product-type {
    font-size: 0.75rem;
    color: #728A9B;
    margin-bottom: 0.75rem;
    font-style: italic;
  }

  // Stock information
  .stock-info {
    margin-bottom: 0.75rem;

    .stock-text {
      font-size: 0.875rem;
      font-weight: 500;

      &.in-stock {
        color: #163659;
      }

      &.out-of-stock {
        color: #F80D38;
        font-weight: 600;
      }
    }
  }

  // Product price
  .product-price {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1061AC;
    text-align: left;
    margin-top: auto;
  }
}

// Responsive design for cards
@media (max-width: 768px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 0.75rem;

    .product-card {
      padding: 0.75rem;
      min-height: 240px;

      .product-image-container {
        height: 100px;
        margin-bottom: 0.5rem;
      }

      .product-header {
        margin-bottom: 0.375rem;

        .product-name {
          font-size: 0.75rem;
        }

        .header-actions {
          gap: 0.375rem;

          .toggle-switch {
            width: 28px;
            height: 16px;

            .toggle-slider {
              width: 12px;
              height: 12px;
              top: 2px;
              left: 2px;
            }

            &.active .toggle-slider {
              transform: translateX(12px);
            }
          }

          .menu-container .menu-dropdown {
            min-width: 120px;

            .menu-item {
              padding: 0.5rem 0.75rem;
              font-size: 0.75rem;

              .menu-icon {
                width: 12px;
                height: 12px;
              }
            }
          }
        }
      }

      .product-type {
        font-size: 0.6875rem;
        margin-bottom: 0.5rem;
      }

      .stock-info {
        margin-bottom: 0.5rem;

        .stock-text {
          font-size: 0.75rem;
        }
      }

      .product-price {
        font-size: 1rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 0.5rem;

    .product-card {
      padding: 0.5rem;
      min-height: 220px;

      .product-image-container {
        height: 80px;
        margin-bottom: 0.375rem;
      }

      .product-header {
        .product-name {
          font-size: 0.6875rem;
        }

        .header-actions {
          gap: 0.25rem;

          .toggle-switch {
            width: 24px;
            height: 14px;

            .toggle-slider {
              width: 10px;
              height: 10px;
            }

            &.active .toggle-slider {
              transform: translateX(10px);
            }
          }

          .menu-container .menu-dropdown {
            min-width: 100px;

            .menu-item {
              padding: 0.375rem 0.5rem;
              font-size: 0.6875rem;

              .menu-icon {
                width: 10px;
                height: 10px;
              }
            }
          }
        }
      }

      .product-type {
        font-size: 0.625rem;
      }

      .stock-info .stock-text {
        font-size: 0.6875rem;
      }

      .product-price {
        font-size: 0.875rem;
      }
    }
  }
}

// Badge styles
.badge-in-stock {
  background-color: #d4edda;
  color: #155724;
}

.badge-out-of-stock {
  background-color: #f8d7da;
  color: #721c24;
}

.badge-active {
  background-color: #d4edda;
  color: #155724;
}

.badge-inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.badge-unknown {
  background-color: #e2e3e5;
  color: #383d41;
}

// Pagination styles (copied from prescriptions)
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  margin-top: 20px;
  border-top: 1px solid #E8EDF5;
  gap: 20px;
  flex-wrap: wrap;
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-label {
  font-size: 14px;
  color: #163659;
  font-weight: 500;
}

.page-size-selector {
  padding: 6px 12px;
  border: 1px solid #C7DAEC;
  border-radius: 6px;
  background: white;
  color: #163659;
  font-size: 14px;
  cursor: pointer;
  min-width: 60px;
}

.page-size-selector:focus {
  outline: none;
  border-color: #1061AC;
  box-shadow: 0 0 0 2px rgba(16, 97, 172, 0.1);
}

.pagination-info {
  flex: 1;
  text-align: center;
}

.pagination-text {
  font-size: 14px;
  color: #728A9B;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid #C7DAEC;
  background: white;
  color: #163659;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
  background: #F5F7FA;
  border-color: #1061AC;
  color: #1061AC;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #F8F9FA;
}

.pagination-btn.page-number.active {
  background: #1061AC;
  border-color: #1061AC;
  color: white;
}

.pagination-btn.page-number.active:hover {
  background: #0D4E8C;
  border-color: #0D4E8C;
}

.pagination-btn svg {
  width: 16px;
  height: 16px;
}

// Responsive pagination
@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .pagination-info {
    text-align: center;
    order: -1;
  }

  .items-per-page {
    justify-content: center;
  }

  .pagination-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
}

/* Custom Modal Styles for Edit Product */
::ng-deep .custom-modal {
  z-index: 10000 !important;

  .mat-mdc-dialog-container {
    padding: 0 !important;
    background: transparent !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    overflow: hidden !important;
    width: 950px !important;
    height: 550px !important;
    max-width: 950px !important;
    max-height: 550px !important;
    z-index: 10001 !important;
  }

  .mat-mdc-dialog-surface {
    padding: 0 !important;
    background: transparent !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    overflow: hidden !important;
    width: 950px !important;
    height: 550px !important;
    max-width: 950px !important;
    max-height: 550px !important;
    z-index: 10002 !important;
  }
}
