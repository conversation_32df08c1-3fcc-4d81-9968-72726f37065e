import { TestBed } from '@angular/core/testing';
import { RouteEncryptionService } from './route-encryption.service';

describe('RouteEncryptionService', () => {
  let service: RouteEncryptionService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(RouteEncryptionService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should encrypt and decrypt IDs correctly', () => {
    const originalId = '12345';
    const encryptedId = service.encryptId(originalId);
    const decryptedId = service.decryptId(encryptedId);

    expect(encryptedId).not.toBe(originalId);
    expect(decryptedId).toBe(originalId);
  });

  it('should handle different ID formats', () => {
    const testIds = [
      '1',
      '123',
      'abc-def-ghi',
      'user_12345',
      'prescription-2024-001'
    ];

    testIds.forEach(id => {
      const encrypted = service.encryptId(id);
      const decrypted = service.decryptId(encrypted);
      expect(decrypted).toBe(id);
    });
  });

  it('should produce URL-safe encrypted strings', () => {
    const testId = 'test-id-123';
    const encrypted = service.encryptId(testId);
    
    // Should not contain characters that need URL encoding
    expect(encrypted).not.toContain('+');
    expect(encrypted).not.toContain('/');
    expect(encrypted).not.toContain('=');
  });

  it('should detect encrypted values', () => {
    const originalId = 'test123';
    const encryptedId = service.encryptId(originalId);
    
    expect(service.isEncrypted(encryptedId)).toBe(true);
    expect(service.isEncrypted(originalId)).toBe(false);
  });

  it('should handle encryption errors gracefully', () => {
    // Test with empty string
    const encrypted = service.encryptId('');
    expect(encrypted).toBeDefined();
    
    // Test decryption with invalid input
    const decrypted = service.decryptId('invalid-encrypted-string');
    expect(decrypted).toBeDefined();
  });
});
