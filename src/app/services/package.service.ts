import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {
  Package,
  CreatePackageRequest,
  PackageResponse,
  PackageCreateResponse,
  PackageListResponse
} from '../models/package.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PackageService {
  // Package API is on port 3001 while other APIs are on 3000
  private readonly packageApiUrl = 'http://localhost:3001/pharmacy/package';
  private readonly apiUrl = `${environment.apiUrl}/pharmacy/package`;

  // State management
  private packagesSubject = new BehaviorSubject<Package[]>([]);
  public packages$ = this.packagesSubject.asObservable();

  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  private errorSubject = new BehaviorSubject<string | null>(null);
  public error$ = this.errorSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Get authentication headers for regular API calls (uses localStorage token)
  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('authToken');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Get authentication headers specifically for package service (port 3001)
  private getPackageServiceAuthHeaders(): HttpHeaders {
    // Use the token from the package service backend (port 3001)
    const packageServiceToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************.16DtmJBHpoyo0pD1CLdB75Y3ljrpzjBKn_7ZXYY4ZXw';

    return new HttpHeaders({
      'Authorization': `Bearer ${packageServiceToken}`,
      'Content-Type': 'application/json'
    });
  }

  // Create a new package
  createPackage(packageData: CreatePackageRequest): Observable<Package> {
    const headers = this.getPackageServiceAuthHeaders(); // Use package service specific token

    // Use the correct package API URL (port 3001)
    return this.http.post<PackageCreateResponse>(`${this.packageApiUrl}/create`, packageData, { headers })
      .pipe(
        map(response => {
          if (response.success && response.package) {
            // Add to local state if needed
            const currentPackages = this.packagesSubject.value;
            this.packagesSubject.next([...currentPackages, response.package]);

            return response.package;
          }

          throw new Error('Failed to create package');
        }),
        catchError(error => {
          const errorMessage = error.error?.message || error.message || 'Erreur lors de la création du colis';
          this.errorSubject.next(errorMessage);
          return throwError(() => error);
        })
      );
  }

  // Get all packages (if needed for future use)
  getAllPackages(): Observable<PackageListResponse> {
    this.loadingSubject.next(true);
    this.errorSubject.next(null);

    const headers = this.getAuthHeaders();

    return this.http.get<PackageListResponse>(`${this.apiUrl}/getAll`, { headers })
      .pipe(
        map(response => {
          if (response.success && response.packages) {
            this.packagesSubject.next(response.packages);
            this.loadingSubject.next(false);
            return response;
          }

          this.loadingSubject.next(false);
          return response;
        }),
        catchError(error => {
          this.errorSubject.next('Erreur lors du chargement des colis');
          this.loadingSubject.next(false);
          return throwError(() => error);
        })
      );
  }

  // Get package by ID (if needed for future use)
  getPackageById(packageId: string): Observable<Package> {
    const headers = this.getAuthHeaders();

    return this.http.get<PackageResponse>(`${this.apiUrl}/get/${packageId}`, { headers })
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }

          throw new Error(response.message || 'Failed to fetch package');
        }),
        catchError(error => {
          return throwError(() => error);
        })
      );
  }

  // Clear error state
  clearError(): void {
    this.errorSubject.next(null);
  }

  // Clear packages state
  clearPackages(): void {
    this.packagesSubject.next([]);
  }
}
