import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {
  Pharmacy,
  PharmacyResponse
} from '../models/pharmacy.model';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class PharmacyService {
  private readonly apiUrl = `${environment.apiUrl}/pharmacy`;

  // State management
  private pharmaciesSubject = new BehaviorSubject<Pharmacy[]>([]);
  public pharmacies$ = this.pharmaciesSubject.asObservable();

  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  // Get authentication headers
  private getAuthHeaders(): HttpHeaders {
    return this.authService.getAuthHeaders();
  }

  // Get pharmacy by ID (matching your existing endpoint)
  getPharmacyById(pharmacyId: string): Observable<Pharmacy> {
    const headers = this.getAuthHeaders();
    const url = `${this.apiUrl}/getPharmacyInfo/${pharmacyId}`;

    console.log('Fetching pharmacy:', pharmacyId);

    return this.http.get<PharmacyResponse>(url, { headers })
      .pipe(
        map(response => {
          console.log('Pharmacy response:', response);

          if (response.success && response.data) {
            const pharmacy = this.enrichPharmacyData(response.data);
            return pharmacy;
          }
          throw new Error('Failed to fetch pharmacy');
        }),
        catchError(error => {
          console.error('Get pharmacy error:', error);
          return throwError(() => error);
        })
      );
  }

  // Get pharmacy info by pharmacist email
  getPharmacyInfoByEmail(pharmacistEmail: string): Observable<Pharmacy> {
    const headers = this.getAuthHeaders();
    const url = `${this.apiUrl}/getPharmacyInfoByEmail/${pharmacistEmail}`;

    console.log('Fetching pharmacy by email:', pharmacistEmail);

    return this.http.get<PharmacyResponse>(url, { headers })
      .pipe(
        map(response => {
          console.log('Pharmacy by email response:', response);

          if (response.success && response.data) {
            const pharmacy = this.enrichPharmacyData(response.data);
            return pharmacy;
          }
          throw new Error('Failed to fetch pharmacy by email');
        }),
        catchError(error => {
          console.error('Get pharmacy by email error:', error);
          return throwError(() => error);
        })
      );
  }

  // Enrich pharmacy data with computed properties
  private enrichPharmacyData(pharmacy: Pharmacy): Pharmacy {
    return {
      ...pharmacy,
      fullName: `${pharmacy.firstName} ${pharmacy.lastName}`,
      displayAddress: this.formatAddress(pharmacy.address),
      isOpen: this.isPharmacyOpen(pharmacy)
    };
  }

  // Format pharmacy address for display
  private formatAddress(address: any): string {
    if (!address) {
      return 'Adresse non disponible';
    }

    const parts: string[] = [];
    if (address.address) parts.push(address.address);
    if (address.city) parts.push(address.city);
    if (address.province) parts.push(address.province);
    if (address.postalCode) parts.push(address.postalCode);

    return parts.length > 0 ? parts.join(', ') : 'Adresse non disponible';
  }

  // Check if pharmacy is currently open
  private isPharmacyOpen(pharmacy: Pharmacy): boolean {
    if (!pharmacy.startTime || !pharmacy.endTime) {
      return true; // Assume open if no hours specified
    }
    
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    // Parse start and end times (assuming format like "09:00")
    const startParts = pharmacy.startTime.split(':');
    const endParts = pharmacy.endTime.split(':');
    
    const startMinutes = parseInt(startParts[0]) * 60 + parseInt(startParts[1]);
    const endMinutes = parseInt(endParts[0]) * 60 + parseInt(endParts[1]);
    
    return currentTime >= startMinutes && currentTime <= endMinutes;
  }

  // Get pharmacy display name
  getPharmacyDisplayName(pharmacy: Pharmacy): string {
    return pharmacy.pharmacyName || `Pharmacie ${pharmacy.firstName} ${pharmacy.lastName}`;
  }

  // Get pharmacist full name
  getPharmacistFullName(pharmacy: Pharmacy): string {
    return `${pharmacy.firstName} ${pharmacy.lastName}`;
  }

  // Get pharmacy status text
  getPharmacyStatusText(pharmacy: Pharmacy): string {
    if (pharmacy.isOpen) {
      return 'Ouvert';
    }
    return 'Fermé';
  }

  // Get pharmacy status badge class
  getPharmacyStatusBadgeClass(pharmacy: Pharmacy): string {
    if (pharmacy.isOpen) {
      return 'badge-open';
    }
    return 'badge-closed';
  }

  // Format pharmacy phone number
  formatPharmacyPhone(phone: string): string {
    if (!phone) return '';
    
    // Basic phone formatting - adjust based on your needs
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  }

  // Get pharmacy hours text
  getPharmacyHours(pharmacy: Pharmacy): string {
    if (!pharmacy.startTime || !pharmacy.endTime) {
      return 'Horaires non spécifiés';
    }
    return `${pharmacy.startTime} - ${pharmacy.endTime}`;
  }

  // Check if pharmacy has exceptional closure today
  hasExceptionalClosureToday(pharmacy: Pharmacy): boolean {
    if (!pharmacy.exceptionalClosure || pharmacy.exceptionalClosure.length === 0) {
      return false;
    }
    
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    
    return pharmacy.exceptionalClosure.some(closure => {
      const closureDate = new Date(closure.date).toISOString().split('T')[0];
      return closureDate === today;
    });
  }

  // Get pharmacy role display text
  getPharmacyRoleText(role?: string): string {
    switch (role) {
      case 'OWNER': return 'Propriétaire';
      case 'MANAGER': return 'Gestionnaire';
      case 'PHARMACIST': return 'Pharmacien';
      case 'ASSISTANT': return 'Assistant';
      default: return 'Non spécifié';
    }
  }

  // Get pharmacy plan display text
  getPlanDisplayText(plan: string): string {
    // Adjust based on your plan types
    switch (plan?.toLowerCase()) {
      case 'basic': return 'Plan de base';
      case 'premium': return 'Plan premium';
      case 'enterprise': return 'Plan entreprise';
      default: return plan || 'Plan non spécifié';
    }
  }
}
