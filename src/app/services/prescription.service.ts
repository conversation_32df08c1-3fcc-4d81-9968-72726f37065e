import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, Subject, throwError, forkJoin } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import {
  Prescription,

  CreatePrescriptionByAdminRequest,
  UpdatePrescriptionRequest,
  PrescriptionFilters,
  ApiResponse,
  FileUploadResponse,
  PrescriptionListResponse,
  PrescriptionDetailResponse
} from '../models/prescription.model';
import { Patient } from '../models/patient.model';
import { Pharmacy } from '../models/pharmacy.model';
import { PatientService } from './patient.service';
import { PharmacyService } from './pharmacy.service';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PrescriptionService {
  private readonly apiUrl = `${environment.apiUrl}/prescriptions`;

  // State management
  private prescriptionsSubject = new BehaviorSubject<Prescription[]>([]);
  public prescriptions$ = this.prescriptionsSubject.asObservable();

  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  // Listener pattern for component communication
  private _listeners = new Subject<any>();

  constructor(
    private http: HttpClient,
    private patientService: PatientService,
    private pharmacyService: PharmacyService
  ) {}

  // Get authentication headers
  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('authToken');
    console.log('Auth token:', token ? 'Token found' : 'No token found');

    if (!token) {
      console.warn('No authentication token found in localStorage');
    }

    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Upload file only (matching your /uploadFile endpoint)
  uploadFile(file: File): Observable<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('authToken')}`
    });

    return this.http.post<FileUploadResponse>(`${this.apiUrl}/uploadFile`, formData, { headers })
      .pipe(
        catchError(error => {
          console.error('File upload error:', error);
          return throwError(() => error);
        })
      );
  }

  // Get filtered prescriptions (using the new /filter endpoint)
  getPrescriptions(filters?: PrescriptionFilters): Observable<PrescriptionListResponse> {
    this.loadingSubject.next(true);

    let params = new HttpParams();

    // Add date filter if provided
    if (filters?.date) {
      params = params.set('date', filters.date);
    }

    // Add status filter if provided (single status only)
    if (filters?.status) {
      params = params.set('status', filters.status);
    }

    // Add household filter if provided (using hasHousehold parameter)
    if (filters?.hasHousehold !== undefined) {
      params = params.set('hasHousehold', filters.hasHousehold);
    }

    // Note: Pagination will be handled on frontend for now
    // Backend pagination can be added later by uncommenting these lines:
    // if (filters?.page !== undefined) {
    //   params = params.set('page', filters.page.toString());
    // }
    // if (filters?.limit !== undefined) {
    //   params = params.set('limit', filters.limit.toString());
    // }

    const headers = this.getAuthHeaders();
    const url = `${this.apiUrl}/filter`;

    console.log('Making request to:', url);
    console.log('With headers:', headers);
    console.log('With params:', params.toString());
    console.log('Applied filters:', filters);

    return this.http.get<PrescriptionListResponse>(url, { params, headers })
      .pipe(
        map(response => {
          console.log('Raw response:', response);

          if (response.success) {
            console.log('Filtered prescriptions received:', response.prescriptions);
            console.log('Total count:', response.total);
            this.prescriptionsSubject.next(response.prescriptions);
            this.loadingSubject.next(false);
            return response;
          }
          throw new Error('Failed to fetch filtered prescriptions');
        }),
        catchError(error => {
          this.loadingSubject.next(false);
          console.error('Get filtered prescriptions error:', error);
          console.error('Error status:', error.status);
          console.error('Error message:', error.message);
          console.error('Full error:', error);

          if (error.status === 0) {
            console.error('CORS error or network issue. Check if backend is running and CORS is enabled.');
          } else if (error.status === 401) {
            console.error('Authentication error. Check if token is valid.');
          } else if (error.status === 403) {
            console.error('Authorization error. User may not have permission.');
          }

          return throwError(() => error);
        })
      );
  }

  // Get prescription by ID (matching your /get/:id endpoint)
  getPrescriptionById(id: string): Observable<Prescription> {
    const headers = this.getAuthHeaders();

    return this.http.get<PrescriptionDetailResponse>(`${this.apiUrl}/get/${id}`, { headers })
      .pipe(
        map(response => {
          if (response.success && response.prescription) {
            return response.prescription;
          }
          throw new Error('Failed to fetch prescription');
        }),
        catchError(error => {
          console.error('Get prescription by ID error:', error);
          return throwError(() => error);
        })
      );
  }



  // Create prescription by admin (matching your /createByAdmin endpoint)
  createPrescriptionByAdmin(request: CreatePrescriptionByAdminRequest): Observable<Prescription> {
    const headers = this.getAuthHeaders();

    console.log('Creating prescription by admin with request:', request);

    return this.http.post<ApiResponse<Prescription>>(`${this.apiUrl}/createByAdmin`, request, { headers })
      .pipe(
        map(response => {
          console.log('✅ Create by admin response:', response);

          if (response.success && response.prescription) {
            // Immediate lazy loading update
            const newPrescription = response.prescription;
            console.log('🔄 Performing lazy loading update for new prescription:', newPrescription.id);

            // Add to state immediately for lazy loading
            this.addPrescriptionToState(newPrescription);

            // Trigger listener for component refresh
            this.filter('CreationClick');

            // Enrich the prescription asynchronously (won't block the response)
            this.enrichSinglePrescription(newPrescription);

            return newPrescription;
          }
          throw new Error('Failed to create prescription by admin');
        }),
        catchError(error => {
          console.error('Create prescription by admin error:', error);
          return throwError(() => error);
        })
      );
  }

  // Enrich a single prescription asynchronously (lazy loading)
  private enrichSinglePrescription(prescription: Prescription) {
    console.log('🔄 Starting lazy enrichment for prescription:', prescription.id);

    // Fetch patient data
    this.patientService.getPatientById(prescription.patientId).subscribe({
      next: (patient) => {
        const enrichedPrescription = {
          ...prescription,
          patientName: patient.fullName,
          patientInitials: patient.initials,
          patientPhone: patient.phone
        };
        this.updatePrescriptionInState(enrichedPrescription);
        console.log('🔄 Patient data enriched via lazy loading:', prescription.id);
      },
      error: (error) => {
        console.error('❌ Error loading patient data:', error);
      }
    });

    // Fetch pharmacy data
    this.pharmacyService.getPharmacyById(prescription.pharmacyId).subscribe({
      next: (pharmacy) => {
        const currentPrescriptions = this.prescriptionsSubject.value;
        const prescriptionToUpdate = currentPrescriptions.find(p => p.id === prescription.id);
        if (prescriptionToUpdate) {
          const enrichedPrescription = {
            ...prescriptionToUpdate,
            pharmacyName: pharmacy.pharmacyName
          };
          this.updatePrescriptionInState(enrichedPrescription);
          console.log('🔄 Pharmacy data enriched via lazy loading:', prescription.id);
        }
      },
      error: (error) => {
        console.error('❌ Error loading pharmacy data:', error);
      }
    });
  }

  // Upload file and create prescription workflow
  uploadFileAndCreatePrescription(
    file: File,
    patientId: string,
    pharmacyId: string,
    note?: string,
    issueDate?: string
  ): Observable<Prescription> {
    console.log('Starting upload and create workflow...');

    // First upload the file
    return this.uploadFile(file).pipe(
      switchMap(uploadResponse => {
        console.log('File uploaded successfully:', uploadResponse);

        if (!uploadResponse.filePath) {
          throw new Error('File upload failed: No file path returned');
        }

        // Then create the prescription with the uploaded file path
        const createRequest: CreatePrescriptionByAdminRequest = {
          patientId,
          pharmacyId,
          storagePaths: [uploadResponse.filePath],
          note,
          issueDate,
          medicines: [] // Will be populated by OCR
        };

        return this.createPrescriptionByAdmin(createRequest);
      }),
      catchError(error => {
        console.error('Upload and create workflow error:', error);
        return throwError(() => error);
      })
    );
  }

  // Add prescription to local state (for lazy loading)
  addPrescriptionToState(prescription: Prescription): void {
    const currentPrescriptions = this.prescriptionsSubject.value;
    const updatedPrescriptions = [prescription, ...currentPrescriptions];
    this.prescriptionsSubject.next(updatedPrescriptions);
    console.log('🔄 Prescription added to local state - lazy loading update');
  }

  // Update prescription in local state (for lazy loading)
  updatePrescriptionInState(updatedPrescription: Prescription): void {
    const currentPrescriptions = this.prescriptionsSubject.value;
    const updatedPrescriptions = currentPrescriptions.map(p =>
      p.id === updatedPrescription.id ? updatedPrescription : p
    );
    this.prescriptionsSubject.next(updatedPrescriptions);
    console.log('🔄 Prescription updated in local state - lazy loading update');
  }

  // Remove prescription from local state (for lazy loading)
  removePrescriptionFromState(prescriptionId: string): void {
    const currentPrescriptions = this.prescriptionsSubject.value;
    const updatedPrescriptions = currentPrescriptions.filter(p => p.id !== prescriptionId);
    this.prescriptionsSubject.next(updatedPrescriptions);
    console.log('🔄 Prescription removed from local state - lazy loading update');
  }

  // Listener pattern methods
  listen(): Observable<any> {
    return this._listeners.asObservable();
  }

  filter(filterBy: string): void {
    console.log('🔔 Broadcasting event:', filterBy);
    this._listeners.next(filterBy);
  }

  // Update prescription (matching your /update/:id endpoint)
  updatePrescription(id: string, request: UpdatePrescriptionRequest): Observable<Prescription> {
    const headers = this.getAuthHeaders();

    return this.http.put<ApiResponse<Prescription>>(`${this.apiUrl}/update/${id}`, request, { headers })
      .pipe(
        map(response => {
          if (response.success && response.data) {
            // Update local state
            const currentPrescriptions = this.prescriptionsSubject.value;
            const updatedPrescriptions = currentPrescriptions.map(p =>
              p.id === id ? response.data! : p
            );
            this.prescriptionsSubject.next(updatedPrescriptions);
            return response.data;
          }
          throw new Error('Failed to update prescription');
        }),
        catchError(error => {
          console.error('Update prescription error:', error);
          return throwError(() => error);
        })
      );
  }

  // Delete prescription (matching your /delete/:id endpoint)
  deletePrescription(id: string): Observable<void> {
    const headers = this.getAuthHeaders();

    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/delete/${id}`, { headers })
      .pipe(
        map(response => {
          if (response.success) {
            // Update local state
            const currentPrescriptions = this.prescriptionsSubject.value;
            const filteredPrescriptions = currentPrescriptions.filter(p => p.id !== id);
            this.prescriptionsSubject.next(filteredPrescriptions);
            return;
          }
          throw new Error('Failed to delete prescription');
        }),
        catchError(error => {
          console.error('Delete prescription error:', error);
          return throwError(() => error);
        })
      );
  }

  // Download prescription file
  downloadPrescriptionFile(filePath: string): Observable<Blob> {
    // Use direct backend URL for file downloads
    const fullUrl = `${environment.apiUrl}${filePath}`;
    console.log('🔍 Downloading file from:', fullUrl);

    // Add headers to help with CORS
    return this.http.get(fullUrl, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/pdf,*/*',
        'Cache-Control': 'no-cache'
      }
    });
  }

  // Utility methods
  formatDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('fr-FR', {
      weekday: 'short',
      year: 'numeric',
      month: 'long',
      day: '2-digit'
    });
  }

  getStatusBadgeClass(status: string): string {
    switch (status.toUpperCase()) {
      case 'UPLOADED': return 'badge-uploaded';
      case 'IN_REVIEW': return 'badge-in-review';
      case 'NEEDS_CLARIFICATION': return 'badge-needs-clarification';
      case 'APPROVED': return 'badge-approved';
      case 'REJECTED': return 'badge-rejected';
      case 'EXPIRED': return 'badge-expired';
      case 'ARCHIVED': return 'badge-archived';
      default: return 'badge-default';
    }
  }

  getStatusText(status: string): string {
    switch (status.toUpperCase()) {
      case 'UPLOADED': return 'Téléchargée';
      case 'IN_REVIEW': return 'En révision';
      case 'NEEDS_CLARIFICATION': return 'Clarification requise';
      case 'APPROVED': return 'Approuvée';
      case 'REJECTED': return 'Rejetée';
      case 'EXPIRED': return 'Expirée';
      case 'ARCHIVED': return 'Archivée';
      default: return status;
    }
  }

  // Get file URL for display
  getFileUrl(filePath: string): string {
    console.log('🔍 Service - Original file path:', filePath);

    // Use direct backend URL (proxy can be added later if needed)
    const directPath = encodeURI(`${environment.apiUrl}${filePath}`);
    console.log('🔍 Service - Using direct backend URL:', directPath);
    return directPath;
  }



  // Check if file is image
  isImageFile(filePath: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.some(ext => filePath.toLowerCase().endsWith(ext));
  }

  // Check if file is PDF
  isPdfFile(filePath: string): boolean {
    return filePath.toLowerCase().endsWith('.pdf');
  }



  // Get enriched prescription by ID
  getEnrichedPrescriptionById(id: string): Observable<Prescription> {
    return this.getPrescriptionById(id).pipe(
      switchMap(prescription => {
        // Fetch patient and pharmacy data in parallel
        return forkJoin({
          patient: this.patientService.getPatientById(prescription.patientId),
          pharmacy: this.pharmacyService.getPharmacyById(prescription.pharmacyId)
        }).pipe(
          map(({ patient, pharmacy }) => {
            // Enrich prescription with patient and pharmacy data
            return {
              ...prescription,
              patientName: patient.fullName,
              patientInitials: patient.initials,
              patientPhone: patient.phone,
              pharmacyName: pharmacy.pharmacyName
            };
          }),
          catchError(error => {
            console.error('Error enriching prescription:', error);
            // Return prescription with fallback values
            return [{
              ...prescription,
              patientName: `Patient #${prescription.patientId}`,
              patientInitials: 'PA',
              patientPhone: 'N/A',
              pharmacyName: `Pharmacie #${prescription.pharmacyId}`
            }];
          })
        );
      })
    );
  }
}
