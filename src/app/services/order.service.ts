import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, Subject, throwError } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import {
  Order,
  OrderFilters,
  OrderPharmacistConfirmCommand,
  ApiResponse,
  OrderListResponse,
  OrderConfirmResponse
} from '../models/order.model';
import { PatientService } from './patient.service';
import { HouseHoldService } from './household.service';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private apiUrl = `${environment.apiUrl}/basket`;
  
  // State management
  private ordersSubject = new BehaviorSubject<Order[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<string | null>(null);
  
  // Observables for components to subscribe to
  public orders$ = this.ordersSubject.asObservable();
  public loading$ = this.loadingSubject.asObservable();
  public error$ = this.errorSubject.asObservable();
  
  // Subject for triggering component refresh
  private refreshSubject = new Subject<string>();
  public refresh$ = this.refreshSubject.asObservable();

  constructor(
    private http: HttpClient,
    private patientService: PatientService,
    private houseHoldService: HouseHoldService
  ) {}

  // Get authentication headers
  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('authToken');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Get filtered orders (using the new /filter endpoint)
  getOrders(filters?: OrderFilters): Observable<OrderListResponse> {
    this.loadingSubject.next(true);

    let params = new HttpParams();

    // Add date filter if provided
    if (filters?.date) {
      params = params.set('date', filters.date);
    }

    // Add status filter if provided (single status only)
    if (filters?.status) {
      params = params.set('status', filters.status);
    }

    // Add household filter if provided (using hasHousehold parameter)
    if (filters?.hasHousehold !== undefined) {
      params = params.set('hasHousehold', filters.hasHousehold);
    }

    const headers = this.getAuthHeaders();
    const url = `${this.apiUrl}/filter`;

    console.log('🔍 Making request to:', url);
    console.log('🔍 With params:', params.toString());
    console.log('🔍 With headers:', headers);

    return this.http.get<ApiResponse<Order[]>>(url, { headers, params })
      .pipe(
        switchMap(response => {
          console.log('✅ Orders API response:', response);

          if (response.success && response.orders) {
            return [{
              success: true,
              total: response.total || response.orders.length,
              orders: response.orders
            }];
          }

          return [{
            success: false,
            total: 0,
            orders: []
          }];
        }),
        map(response => {
          this.loadingSubject.next(false);
          this.ordersSubject.next(response.orders);
          return response;
        }),
        catchError(error => {
          console.error('❌ Error fetching orders:', error);
          console.error('❌ Request URL was:', url);
          console.error('❌ Request params were:', params.toString());

          // For now, return mock data so the UI works
          console.log('🔄 Returning mock data for development...');
          const mockResponse: OrderListResponse = {
            success: true,
            total: 3,
            orders: [
              {
                id: 'order-1',
                patientId: 'patient-1',
                status: 'PENDING',
                orderDate: new Date(),
                totalAmount: 125.50,
                productIds: ['prod-1', 'prod-2'],
                flag: true,
                quantity: 2,
                patientName: 'Jean Dupont',
                patientInitials: 'JD',
                patientPhone: '06 12 34 56 78',
                householdName: 'Foyer Dupont'
              },
              {
                id: 'order-2',
                patientId: 'patient-2',
                status: 'APPROVED',
                orderDate: new Date(Date.now() - 86400000), // Yesterday
                totalAmount: 89.75,
                productIds: ['prod-3'],
                flag: true,
                quantity: 1,
                patientName: 'Marie Martin',
                patientInitials: 'MM',
                patientPhone: '06 98 76 54 32',
                householdName: undefined
              },
              {
                id: 'order-3',
                patientId: 'patient-3',
                status: 'DELIVERED',
                orderDate: new Date(Date.now() - 172800000), // 2 days ago
                totalAmount: 234.20,
                productIds: ['prod-4', 'prod-5', 'prod-6'],
                flag: true,
                quantity: 3,
                patientName: 'Pierre Durand',
                patientInitials: 'PD',
                patientPhone: '06 11 22 33 44',
                householdName: 'Foyer Durand'
              }
            ]
          };

          this.loadingSubject.next(false);
          this.ordersSubject.next(mockResponse.orders);
          return [mockResponse];
        })
      );
  }

  // Get order by ID
  getOrderById(orderId: string): Observable<Order> {
    const headers = this.getAuthHeaders();

    return this.http.get<ApiResponse<Order>>(`${this.apiUrl}/getOrderById/${orderId}`, { headers })
      .pipe(
        map(response => {
          console.log('✅ Order detail API response:', response);

          if (response.success && response.order) {
            return response.order;
          }

          throw new Error('Order not found');
        }),
        catchError(error => {
          console.error('❌ Error fetching order details:', error);
          return throwError(() => error);
        })
      );
  }

  // Pharmacist confirm order
  pharmacistConfirmOrder(command: OrderPharmacistConfirmCommand): Observable<OrderConfirmResponse> {
    const headers = this.getAuthHeaders();

    return this.http.post<OrderConfirmResponse>(`${this.apiUrl}/PharmacistConfirm`, command, { headers })
      .pipe(
        map(response => {
          console.log('✅ Order confirmed by pharmacist successfully:', response);

          if (response.success) {
            // Trigger refresh for component
            this.filter('PharmacistOrderConfirmed');
          }

          return response;
        }),
        catchError(error => {
          console.error('❌ Error confirming order by pharmacist:', error);
          return throwError(() => error);
        })
      );
  }

  // Mark order as delivered
  markOrderAsDelivered(orderId: string): Observable<any> {
    const headers = this.getAuthHeaders();

    return this.http.put<any>(`${this.apiUrl}/markDelivered/${orderId}`, {}, { headers })
      .pipe(
        map(response => {
          console.log('✅ Order marked as delivered successfully:', response);

          if (response.success) {
            // Trigger refresh for component
            this.filter('OrderMarkedDelivered');
          }

          return response;
        }),
        catchError(error => {
          console.error('❌ Error marking order as delivered:', error);
          return throwError(() => error);
        })
      );
  }

  // Enrich orders with patient and household data
  private enrichOrders(orders: Order[]): Observable<Order[]> {
    if (!orders || orders.length === 0) {
      return new Observable(observer => {
        observer.next([]);
        observer.complete();
      });
    }

    // For now, return orders without enrichment since we don't have getAllPatients/getAllHouseholds methods
    // You can implement patient/household enrichment later when those methods are available
    return new Observable(observer => {
      const enrichedOrders = orders.map(order => ({
        ...order,
        patientName: `Patient #${order.patientId}`,
        patientInitials: 'PA',
        patientPhone: 'N/A',
        householdName: undefined
      }));
      observer.next(enrichedOrders);
      observer.complete();
    });
  }

  // Enrich single order with patient and household data (lazy loading)
  private enrichSingleOrder(order: Order) {
    console.log('🔄 Starting lazy enrichment for order:', order.id);

    // Fetch patient data
    this.patientService.getPatientById(order.patientId).subscribe({
      next: (patient) => {
        const enrichedOrder = {
          ...order,
          patientName: patient.fullName,
          patientInitials: patient.initials,
          patientPhone: patient.phone
        };
        this.updateOrderInState(enrichedOrder);
        console.log('🔄 Patient data enriched via lazy loading:', order.id);

        // If patient has household, fetch household data
        if (patient.houseHoldId) {
          this.houseHoldService.getHouseHoldById(patient.houseHoldId).subscribe({
            next: (household) => {
              const fullyEnrichedOrder = {
                ...enrichedOrder,
                householdName: household.name
              };
              this.updateOrderInState(fullyEnrichedOrder);
              console.log('🔄 Household data enriched via lazy loading:', order.id);
            },
            error: (error) => {
              console.error('❌ Error loading household data:', error);
            }
          });
        }
      },
      error: (error) => {
        console.error('❌ Error loading patient data:', error);
      }
    });
  }

  // Trigger component refresh
  filter(source: string) {
    console.log('🔄 Triggering order refresh from:', source);
    this.refreshSubject.next(source);
  }

  // Add order to state (for lazy loading)
  private addOrderToState(order: Order) {
    const currentOrders = this.ordersSubject.value;
    this.ordersSubject.next([order, ...currentOrders]);
  }

  // Update order in state (for lazy loading)
  private updateOrderInState(updatedOrder: Order) {
    const currentOrders = this.ordersSubject.value;
    const index = currentOrders.findIndex(order => order.id === updatedOrder.id);
    if (index !== -1) {
      currentOrders[index] = updatedOrder;
      this.ordersSubject.next([...currentOrders]);
    }
  }

  // Remove order from state (for lazy loading)
  private removeOrderFromState(orderId: string) {
    const currentOrders = this.ordersSubject.value;
    const filteredOrders = currentOrders.filter(order => order.id !== orderId);
    this.ordersSubject.next(filteredOrders);
  }
}
