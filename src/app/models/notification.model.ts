// Notification Type enum matching backend
export enum NotificationType {
  PRESCRIPTION_ADDED = 'PRESCRIPTION_ADDED',
  PACKAGE_READY = 'PACKAGE_READY',
  DELIVERY_ASSIGNED = 'DELIVERY_ASSIGNED',
  DELIVERY_COMPLETED = 'DELIVERY_COMPLETED',
  GENERAL = 'GENERAL'
}

// Notification Status enum matching backend
export enum NotificationStatus {
  UNREAD = 'UNREAD',
  READ = 'READ',
  ARCHIVED = 'ARCHIVED'
}

// Main Notification interface matching backend entity
export interface Notification {
  _id?: string; // MongoDB ID
  id: string;
  recipientId: string; // User ID who will receive the notification
  senderId?: string; // User ID who triggered the notification (optional)
  title: string;
  message: string;
  type: NotificationType;
  status: NotificationStatus;
  data?: any; // Additional data related to the notification (prescription ID, package ID, etc.)
  fcmSent: boolean; // Whether FCM notification was sent successfully
  fcmMessageId?: string; // FCM message ID for tracking
  readAt?: Date | string; // When the notification was read
  archivedAt?: Date | string; // When the notification was archived
  createdAt?: Date | string;
  updatedAt?: Date | string;
  __v?: number; // MongoDB version field
}

// Get User Notifications Command interface
export interface GetUserNotificationsCommand {
  userId: string;
  status?: NotificationStatus;
  type?: NotificationType;
  page?: number;
  limit?: number;
}

// Mark Notification As Read Command interface
export interface MarkNotificationAsReadCommand {
  notificationId: string;
  userId: string;
}

// Update FCM Token Command interface
export interface UpdateFcmTokenCommand {
  userId: string;
  fcmToken: string;
}

// Send Notification Command interface
export interface SendNotificationCommand {
  recipientId: string;
  senderId?: string;
  title: string;
  message: string;
  type: NotificationType;
  data?: any;
}

// API Response interfaces
export interface NotificationResponse {
  success: boolean;
  message: string;
  data?: any;
}

export interface GetNotificationsResponse extends NotificationResponse {
  data: {
    notifications: Notification[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    unreadCount: number;
  };
}

export interface MarkAsReadResponse extends NotificationResponse {
  data: Notification;
}

export interface UpdateFcmTokenResponse extends NotificationResponse {
  data: {
    fcmToken: string;
  };
}

export interface SendNotificationResponse extends NotificationResponse {
  data: Notification;
}

// FCM Token interface
export interface FcmTokenData {
  token: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Push notification payload interface
export interface PushNotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: any;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

// French translations for notification types
export const NotificationTypeTranslations: Record<NotificationType, string> = {
  [NotificationType.PRESCRIPTION_ADDED]: 'Nouvelle ordonnance',
  [NotificationType.PACKAGE_READY]: 'Colis prêt',
  [NotificationType.DELIVERY_ASSIGNED]: 'Livraison assignée',
  [NotificationType.DELIVERY_COMPLETED]: 'Livraison terminée',
  [NotificationType.GENERAL]: 'Notification générale'
};

// French translations for notification status
export const NotificationStatusTranslations: Record<NotificationStatus, string> = {
  [NotificationStatus.UNREAD]: 'Non lu',
  [NotificationStatus.READ]: 'Lu',
  [NotificationStatus.ARCHIVED]: 'Archivé'
};
