// User Role enum matching backend
export enum Role {
  PATIENT = 1,
  INTERNAL_DELIVERY_MAN = 2,
  EXTERNAL_DELIVERY_MAN = 3,
  INTERNAL_PHARMACY = 4,
  EXTERNAL_PHARMACY = 5,
  ADMIN = 6
}

// Main User interface matching backend entity
export interface User {
  _id?: string; // MongoDB ID
  id: string;
  email: string;
  password?: string; // Usually not returned from API
  role: Role;
  phone: string;
  fcm?: string; // Firebase Cloud Messaging Token
  createdAt?: Date | string;
  updatedAt?: Date | string;
  __v?: number; // MongoDB version field
}

// User Properties interface matching backend entity
export interface UserProperties {
  _id?: string; // MongoDB ID
  id: string;
  profilePhoto?: string;
  resetCode?: string;
  resetCodeExpiresAt?: Date | string;
  isTwoFactorEnabled?: boolean;
  twoFactorCode?: string;
  twoFactorExpiresAt?: Date | string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  __v?: number; // MongoDB version field
}

// Login request interface
export interface LoginRequest {
  email: string;
  password: string;
}

// Login response interface matching backend
export interface LoginResponse {
  token: string | any; // Allow for cases where backend returns token as object
  message: string;
  requiresPasswordReset?: boolean;
}

// JWT Token payload interface
export interface JwtPayload {
  email: string;
  id: string;
  role: Role;
  phoneNumber: string;
  isTwoFactorEnabled: boolean;
  iat?: number; // Issued at
  exp?: number; // Expires at
}

// Auth state interface for the application
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  userProperties: UserProperties | null;
  token: string | null;
  requiresPasswordReset: boolean;
}

// Password reset request interface
export interface PasswordResetRequest {
  email: string;
}

// Password reset confirm interface
export interface PasswordResetConfirm {
  email: string;
  resetCode: string;
  newPassword: string;
}

// Change password interface
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// API Error response interface
export interface ApiErrorResponse {
  message: string;
  statusCode?: number;
  error?: string;
}
