// HouseHold Address interface
export interface HouseHoldAddress {
  address: string;
  lat: number;
  long: number;
}

// Recurrence Option interface
export interface HouseHoldRecurrenceOption {
  note: string;
  frequency: string;
  from: Date | string;
  to: Date | string;
}

// Main HouseHold interface matching your backend entity
export interface HouseHold {
  _id?: string; // MongoDB ID
  id: string;
  name: string;
  pharmacyId: string;
  email?: string;
  responsibleName?: string;
  phoneNumber?: string;
  post?: number;
  address: HouseHoldAddress;
  note?: string;
  recurrenceOption?: HouseHoldRecurrenceOption;
  deliveryManNote?: string;
  pharmacyNote?: string;
  createdBy?: string;
  deletedAt?: Date | string;
  deletedBy?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  __v?: number; // MongoDB version field
  
  // Computed properties for display
  displayName?: string;
  displayAddress?: string;
  contactInfo?: string;
}

// API Response for household
export interface HouseHoldResponse {
  success: boolean;
  message?: string;
  data?: HouseHold;
}

// HouseHold list response (for future use)
export interface HouseHoldListResponse {
  success: boolean;
  message?: string;
  data?: HouseHold[];
  total?: number;
}

// HouseHold search response (for search endpoint)
export interface HouseHoldSearchResponse {
  success: boolean;
  total: number;
  households: HouseHold[];
}
