// Package Receiver Type enum
export enum PackageReceiverType {
  PATIENT = 'Patient',
  HOUSEHOLD = 'Household',
  GROUP = 'Group'
}

// Patient Payment Type enum
export enum PatientPaymentType {
  PAYONDELEVERY = 'Paiement à la livraison',
  ACCOUNTCHARGE = 'Charge au compte',
  CREDITCARD = 'Carte de credit'
}

// Recurrence Option Type enum
export enum RecurrenceOptionType {
  NONE = 'Pas de récurrence',
  DAILY = 'Quotidien',
  WEEKLY = 'Hebdomadaire',
  MONTHLY = 'Mensuel',
  CUSTOM = 'Personnalisé'
}

// Package Status enum
export enum PackagesStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  IN_PREPARATION = 'IN_PREPARATION',
  READY = 'READY',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED'
}

// Preference Type enum
export enum PreferenceType {
  NONE = 'Aucun choix',
  BEFORE = 'Avant',
  AFTER = 'Aprés',
  BETWEEN = 'Entre'
}

// Standard Frequency Type enum
export enum StandardFrequencyType {
  DAILY = 'Quotidien',
  WEEKLY = 'Hebdomadaire',
  MONTHLY = 'Mensuel'
}

// Period Type enum
export enum PeriodType {
  DAYS = 'Jours',
  WEEKS = 'Semaines',
  MONTHS = 'Mois'
}

// Address interface
export interface PackageAddress {
  address: string;
  lat: number;
  long: number;
  apartmentNumber?: string;
}

// Authorized Person interface
export interface AuthorizedPerson {
  name: string;
  type: string;
  phone?: string;
}

// Preference interface
export interface PackagePreference {
  type: PreferenceType;
  time?: string;
  from?: string;
  to?: string;
  recipient?: string; // For picking: "Cueillette" or "Retour en pharmacie"
}

// Recurrence Option interface
export interface RecurrenceOption {
  note?: string;
  frequency?: {
    standardFrequency?: StandardFrequencyType;
    each?: number;
    period?: PeriodType;
  };
  from?: Date;
  to?: Date;
  type?: RecurrenceOptionType;
}

// Patient Receiver interface
export interface PatientReceiver {
  patient: string;
  groups: string[];
  paymentType: PatientPaymentType;
  price: number;
  packagePrice?: number;
  additionalPrice?: number;
  signatureType: string;
  fullName?: string;
  deliveryType: string[];
  pharmacyNote: string;
  deliveryManNote: string;
  authorizedPerson: AuthorizedPerson;
  spot: string;
}

// Household Patient interface
export interface HouseholdPatient {
  id: string;
  fullName: string;
  roomNumber?: number;
  paymentType: string;
  signatureType: string;
  deliveryType: string[];
  pharmacyNote: string;
  deliveryManNote: string;
  householdNote: string;
  authorizedPerson: AuthorizedPerson;
  spot: string;
  recipient?: string;
  price?: number;
  packagePrice?: number;
  additionalPrice?: number;
  hospitalizedPatient?: boolean;
  completed?: boolean;
  delivered?: boolean;
  isPrepared?: boolean;
}

// Household Receiver interface
export interface HouseholdReceiver {
  household: string;
  groups: string[];
  patients: HouseholdPatient[];
}

// Main Package interface matching your backend entity
export interface Package {
  _id?: string; // MongoDB ID
  id: string | null;
  pharmacyId?: string;
  priority: boolean;
  isPicking?: boolean;
  receiverType: PackageReceiverType;
  patientReceiver?: PatientReceiver;
  householdReceiver?: HouseholdReceiver;
  groupReceiver?: any; // Define if needed
  deliveryDate: string;
  deliveryTime: string;
  recurrenceOption?: RecurrenceOption;
  recurrenceId?: string;
  deliveryId?: string;
  status?: PackagesStatus;
  address: PackageAddress;
  originAddress?: PackageAddress;
  preference: PackagePreference;
  inRoute?: boolean;
  createdBy: string;
  nbArticle?: number;
  payedForDeliveryMan?: number;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  __v?: number; // MongoDB version field
}

// Create Package Request interface
export interface CreatePackageRequest {
  id: null;
  priority: boolean;
  receiverType: PackageReceiverType;
  address: PackageAddress;
  createdBy: string;
  deliveryDate: string;
  deliveryTime: string;
  groupReceiver: null;
  householdReceiver: null;
  nbArticle: number;
  patientReceiver: PatientReceiver;
  preference: PackagePreference;
  recurrenceId: null;
  deliveryId: null;
  recurrenceOption: RecurrenceOption;
  // Additional fields for picking
  isPicking?: boolean;
}

// API Response for package creation (following the pattern of other services)
export interface PackageCreateResponse {
  success: boolean;
  message: string;
  package: Package;
}

// API Response for package (general)
export interface PackageResponse {
  success: boolean;
  message?: string;
  data?: Package;
}

// Package list response
export interface PackageListResponse {
  success: boolean;
  message?: string;
  packages?: Package[];
  total?: number;
}

// Package creation form data interface
export interface PackageFormData {
  packagePrice: number;
  nbArticle: number;
  preference: PackagePreference;
  deliveryTime: string;
  deliveryDate: string;
  paymentType: PatientPaymentType;
  signatureType: string;
  authorizedPersonName: string;
  authorizedPersonType: string;
  deliveryManNote: string;
  pharmacyNote: string;
  spot: string;
  // Additional fields for picking
  isPicking?: boolean;
}
