// Pharmacy Role enum
export enum PharmacyRole {
  OWNER = 'OWNER',
  MANAGER = 'MANAGER',
  PHARMACIST = 'PHARMACIST',
  ASSISTANT = 'ASSISTANT'
}

// Address interface for pharmacy
export interface PharmacyAddress {
  address: string;
  street?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  country?: string;
  long?: number;
  lat?: number;
}

// Card Info interface
export interface CardInfo {
  paymentMethodId?: string;
  cardName?: string | null;
  token?: string | null;
  billingAddress?: PharmacyAddress;
  couponCode?: string | null;
}

// Exceptional Closure interface
export interface ExceptionalClosure {
  type: string;
  date: string;
}

// Main Pharmacy interface matching your backend entity
export interface Pharmacy {
  _id?: string; // MongoDB ID
  id: string;
  pharmacyName: string;
  firstName: string;
  lastName: string;
  pharmacistPhone: string;
  pharmacistEmail: string;
  address: PharmacyAddress;
  title?: PharmacyRole;
  banner: string;
  plan: string;
  selectedCost: string;
  cardInfo?: CardInfo;
  paymentToken?: any;
  paymentMethods?: any[];
  website?: string;
  exceptionalClosure?: ExceptionalClosure[];
  description?: string;
  notification?: boolean;
  startTime?: string;
  endTime?: string;
  firstLogin: boolean;
  startDate?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  __v?: number; // MongoDB version field
  
  // Computed properties for display
  fullName?: string;
  displayAddress?: string;
  isOpen?: boolean;
}

// API Response for pharmacy
export interface PharmacyResponse {
  success: boolean;
  message?: string;
  data?: Pharmacy;
}

// Pharmacy list response (for future use)
export interface PharmacyListResponse {
  success: boolean;
  message?: string;
  data?: Pharmacy[];
  total?: number;
}
