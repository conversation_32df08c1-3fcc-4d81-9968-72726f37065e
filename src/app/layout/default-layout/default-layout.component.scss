:host ::ng-deep {
  .sidebar {
    background-color: rgb(16, 97, 172) !important;

    .sidebar-header {
      background-color: inherit;
    }

    // Layout structure for sidebar with footer
    &.sidebar-with-footer {
      display: flex;
      flex-direction: column;

      .sidebar-content {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-height: 0; // Important for flexbox scrolling

        .overflow {
          flex: 1;
          min-height: 0; // Important for flexbox scrolling
        }
      }
    }

    .nav-link {
      color: white !important;

      .nav-icon,
      svg,
      i {
        color: white !important;
        fill: white !important;
        stroke: white !important;
      }

      .dropdown-toggle::after {
        content: '▾'; // Visible arrow symbol
        font-size: 0.65rem;
        margin-left: 0.5rem;
        vertical-align: middle;
        display: inline-block;
        color: currentColor !important; // inherit text color
      }

      &:hover,
      &.active,
      &[style*="background-color: #F5FFF9"],
      &[style*="background-color: rgb(245, 255, 249)"] {
        background-color: #F5FFF9 !important;
        color: #1061AC !important;

        .nav-icon,
        svg,
        i {
          color: #1061AC !important;
          fill: #1061AC !important;
          stroke: #1061AC !important;
        }

        .dropdown-toggle::after {
          color: currentColor !important; // inherit text color
        }
      }
    }

    .nav-group {
      border-radius: 0 !important;

      &.show,
      &.open,
      &.expanded,
      &:has(.nav-link.active) {
        background-color: rgba(245, 255, 249, 0.86) !important;
        border-radius: 6px !important;
        > .nav-link {
          background-color: #F5FFF9 !important;
          color: #1061AC !important;
          border-radius: 0 !important;

          .nav-icon,
          .dropdown-toggle::after,
          svg,
          i,
          .icon,
          .caret,
          .arrow {
            color: currentColor !important;
            fill: currentColor !important;
            stroke: currentColor !important;
          }
        }

        .nav-dropdown,
        .nav-dropdown-items,
        .sidebar-nav-dropdown,
        .nav-group-items,
        .collapse {
          display: block !important;
          visibility: visible !important;
          height: auto !important;
          max-height: none !important;
          overflow: visible !important;
          position: relative !important;
          transform: none !important;
          transition: none !important;
          animation: none !important;
          border-radius: 0 !important;

          &::before {
            content: '' !important;
            position: absolute !important;
            top: 0.5rem !important;
            bottom: 0.5rem !important;
            left: 1.25rem !important;
            width: 2px !important;
            background-color: #57B6B1 !important;
            z-index: 0 !important;
          }

          padding-top: 0.5rem !important;
          padding-bottom: 0.5rem !important;
          margin-left: 1.5rem !important;

          .nav-link {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
            background-color: transparent !important;
            color: #163659 !important; // Default dark color for all dropdown items
            padding-left: 3.5rem !important;
            margin: 0 !important;
            position: relative !important;
            z-index: 1 !important;
            border-radius: 0 !important;

            .nav-icon {
              display: none !important;
            }

            .dropdown-toggle::after,
            svg:not(.nav-icon),
            i:not(.nav-icon),
            .icon:not(.nav-icon),
            .caret,
            .arrow {
              color: currentColor !important;
              fill: currentColor !important;
              stroke: currentColor !important;
            }

            // Remove all default active states first
            &.active,
            &[routerLinkActive],
            &.router-link-active {
              color: #163659 !important; // Reset to dark color
            }

            // Hover state - blue color on hover
            &:hover,
            &:focus {
              color: #1061AC !important;
            }

            // Force specific behavior for Commandes vs Gestion du Stock
            &[href="/orders"] {
              // When this exact link is active, make it blue
              &.active {
                color: #1061AC !important;
              }

              // When NOT active (e.g., when /orders/stock is active), keep it dark
              &:not(.active) {
                color: #163659 !important;
              }
            }

            &[href="/orders/stock"] {
              // When this exact link is active, make it blue
              &.active {
                color: #1061AC !important;
              }

              // When NOT active, keep it dark
              &:not(.active) {
                color: #163659 !important;
              }
            }
          }

          // Specific targeting for exact routes
          .nav-link[href="/orders"].active,
          .nav-link[routerLink="/orders"].router-link-active {
            color: #1061AC !important; // Blue only when Commandes is exactly active
          }

          .nav-link[href="/orders/stock"].active,
          .nav-link[routerLink="/orders/stock"].router-link-active {
            color: #1061AC !important; // Blue only when Gestion du Stock is exactly active
          }

          // Override: When on /orders/stock, force /orders link to be dark
          body:has([href="/orders/stock"].active) .nav-link[href="/orders"]:not(.active),
          body:has([routerLink="/orders/stock"].router-link-active) .nav-link[href="/orders"]:not(.active) {
            color: #163659 !important;
          }
        }
      }
    }
  }

  // Emergency Hotline Footer Styles
  .emergency-hotline-footer {
    flex-shrink: 0; // Prevent shrinking
    padding: 1rem;

    .emergency-card {
      background-color: #F5FFF9;
      border-radius: 8px;
      padding: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .emergency-content {
        display: flex;
        align-items: center;
        gap: 12px;

        .emergency-icon-container {
          flex-shrink: 0;
          background-color: #1061AC;
          border-radius: 6px;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          // Signal waves effect
          &::before,
          &::after {
            content: '';
            position: absolute;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: signal-pulse 2s infinite;
          }

          &::before {
            width: 50px;
            height: 50px;
            animation-delay: 0s;
          }

          &::after {
            width: 60px;
            height: 60px;
            animation-delay: 0.5s;
          }

          .emergency-icon {
            width: 20px;
            height: 20px;
            color: #FFFFFF;
            fill: #FFFFFF;
            transform: scaleX(-1); // Mirror horizontally to face left
            position: relative;
            z-index: 1;
          }
        }

        @keyframes signal-pulse {
          0% {
            opacity: 1;
            transform: scale(0.8);
          }
          100% {
            opacity: 0;
            transform: scale(1.2);
          }
        }

        .emergency-text {
          flex: 1;

          .emergency-title {
            font-style: italic;
            color: #57B6B1;
            font-size: 0.875rem;
            line-height: 1.2;
            margin-bottom: 2px;
            font-weight: 400;
          }

          .emergency-phone {
            color: #163659;
            font-size: 0.875rem;
            font-weight: 500;
            line-height: 1.2;
          }
        }
      }
    }
  }

  // Narrow mode styles for emergency hotline footer
  &.sidebar-narrow {
    .emergency-hotline-footer {
      padding: 0.5rem; // Reduce padding

      .emergency-card {
        padding: 8px; // Smaller padding
        border-radius: 6px; // Smaller border radius

        .emergency-content {
          gap: 0; // Remove gap between icon and text
          justify-content: center; // Center the icon

          .emergency-icon-container {
            width: 32px; // Smaller icon container
            height: 32px;
            border-radius: 4px; // Smaller border radius

            // Smaller signal waves for narrow mode
            &::before,
            &::after {
              border-width: 1px; // Thinner border
            }

            &::before {
              width: 38px; // Smaller waves
              height: 38px;
            }

            &::after {
              width: 44px;
              height: 44px;
            }

            .emergency-icon {
              width: 16px; // Smaller icon
              height: 16px;
            }
          }

          // Hide text in narrow mode
          .emergency-text {
            display: none !important;
          }
        }
      }
    }
  }
}
