<!--<c-header class="mb-4 d-print-none" position="sticky">-->
<ng-container>
  <c-container [fluid]="true" class="border-bottom px-4 header-content">
    <!-- Left side: Search Input -->
    <div class="search-container d-flex align-items-center">
      <div class="input-group flex-grow-1 me-auto" style="max-width: 300px;">
        <input type="text" class="form-control rounded-pill-left" placeholder="Recherche" aria-label="Search">
        <button class="btn btn-primary rounded-pill-right" type="button">
          <img src="assets/images/search.svg" alt="Search Icon" style="width: 16px; height: 16px;">
        </button>
      </div>
    </div>

    <!-- Right side: Icons and User Info -->
    <c-header-nav class="ms-auto align-items-center">
      <!-- Notification Icon -->
      <div class="notification-wrapper position-relative">
        <div
          class="notification-icon-wrapper nav-link p-2 position-relative"
          (click)="toggleNotificationDropdown()"
          aria-label="Notifications"
          style="cursor: pointer; border: none; background: none;"
        >
          <img src="assets/images/notification.svg" alt="Notification Icon" style="width: 28px; height: 28px;">
          @if (unreadCount > 0) {
            <c-badge
              color="danger"
              position="top-end"
              shape="rounded-pill"
              class="position-absolute translate-middle"
              style="font-size: 0.7rem; min-width: 18px; height: 18px; top: 6px; right: 6px;"
            >
              {{ unreadCount > 99 ? '99+' : unreadCount }}
            </c-badge>
          }
        </div>

        <!-- Notification Panel -->
        @if (showNotificationDropdown) {
          <div class="notification-panel position-absolute"
               style="top: 100%; right: 0; z-index: 1050; width: 350px; max-height: 400px; overflow-y: auto; background: white; border: 1px solid #C7DAEC; border-radius: 10px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">

            <!-- Panel Header -->
            <div class="d-flex justify-content-between align-items-center px-3 py-2 border-bottom">
              <span class="fw-semibold">Notifications</span>
              @if (unreadCount > 0) {
                <button
                  class="btn btn-sm btn-link text-decoration-none p-0"
                  (click)="markAllNotificationsAsRead()"
                  style="font-size: 0.8rem; color: #1061AC;"
                >
                  Tout marquer comme lu
                </button>
              }
            </div>

            <!-- Notifications List -->
            @if (notifications.length > 0) {
              @for (notification of notifications; track notification.id) {
                <div
                  class="notification-item px-3 py-2"
                  [class.unread]="notification.status === 'UNREAD'"
                  (click)="markNotificationAsRead(notification)"
                  style="cursor: pointer; white-space: normal; border-bottom: 1px solid #E6EDF5;"
                >
                  <div class="d-flex align-items-start">
                    <!-- Patient Avatar -->
                    <div class="patient-avatar me-3 mt-1">
                      <div
                        class="avatar-circle d-flex align-items-center justify-content-center"
                        style="width: 40px; height: 40px; border-radius: 50%; background-color: #F5FFF9; border: 2px solid #57B6B1; font-weight: 600; font-size: 0.9rem; color: #163659;"
                      >
                        {{ getPatientInitials(notification) }}
                      </div>
                    </div>

                    <div class="notification-content flex-grow-1">
                      <div class="notification-title fw-semibold mb-1" style="font-size: 0.9rem; color: #163659;">
                        {{ notification.title }}
                      </div>

                      <div class="notification-message text-muted mb-1" style="font-size: 0.8rem; line-height: 1.3;">
                        {{ notification.message }}
                      </div>

                      <div class="notification-meta d-flex justify-content-between align-items-center">
                        <small class="text-muted" style="font-size: 0.7rem;">
                          {{ formatNotificationTime(notification.createdAt || '') }}
                        </small>
                        <small class="notification-type" style="font-size: 0.7rem; color: #1061AC;">
                          {{ getNotificationTypeLabel(notification.type) }}
                        </small>
                      </div>
                    </div>

                    @if (notification.status === 'UNREAD') {
                      <div class="unread-indicator ms-2 mt-2">
                        <div class="rounded-circle" style="width: 8px; height: 8px; background-color: #1061AC;"></div>
                      </div>
                    }
                  </div>
                </div>
              }
            } @else {
              <div class="text-center py-4 text-muted">
                <svg cIcon name="cilBell" size="xl" class="mb-2 text-muted"></svg>
                <div>Aucune notification</div>
              </div>
            }

            <!-- Panel Footer -->
            <div class="text-center py-2 border-top">
              <button
                class="btn btn-sm btn-link text-decoration-none"
                (click)="refreshNotifications()"
                style="color: #1061AC;"
              >
                <svg cIcon name="cilReload" class="me-1"></svg>
                Actualiser
              </button>
            </div>
          </div>
        }
      </div>

      <!-- Separator after notification -->
      <div class="nav-item py-1">
        <div class="vr h-100 mx-2 text-body text-opacity-75"></div>
      </div>

      <!-- User Info Group -->
      <div class="user-profile-group d-flex align-items-center">
        @if (currentUser) {
          <span class="me-2 user-name">{{ userFullName || currentUser.email }}</span>
          <a class="logout-icon-wrapper nav-link p-2 me-2" (click)="logout()" style="cursor: pointer;">
            <img src="assets/images/logout.png" alt="Logout Icon" style="width: 20px; height: 20px;">
          </a>
          <!-- Separator before avatar -->
          <div class="nav-item py-1">
            <div class="vr h-100 mx-2 text-body text-opacity-75"></div>
          </div>
          <c-avatar
            shape="rounded-1"
            [size]="'md'"
            status="success"
            textColor="primary"
            alt="Pharmacist avatar"
            class="me-2"
          >
            {{ userInitials }}
          </c-avatar>
          <span class="ms-2 pharmacy-name">{{ pharmacyName }}</span>
        }
      </div>
    </c-header-nav>
  </c-container>
</ng-container>
<!--</c-header>-->




