import { NgTemplateOutlet, CommonModule } from '@angular/common';
import { Component, computed, inject, input, OnInit, OnD<PERSON>roy, HostListener } from '@angular/core';

import { Subject, takeUntil } from 'rxjs';

import {
  AvatarComponent,
  BadgeComponent,
  ColorModeService,
  ContainerComponent,
  HeaderComponent,
  HeaderNavComponent
} from '@coreui/angular';

import { IconDirective } from '@coreui/icons-angular';
import { AuthService } from '../../../services/auth.service';
import { PharmacyService } from '../../../services/pharmacy.service';
import { NotificationService } from '../../../services/notification.service';
import { User } from '../../../models/user.model';
import { Pharmacy } from '../../../models/pharmacy.model';
import { Notification, NotificationTypeTranslations } from '../../../models/notification.model';

@Component({
    selector: 'app-default-header',
    templateUrl: './default-header.component.html',
  imports: [
    CommonModule,
    ContainerComponent,
    IconDirective,
    HeaderNavComponent,
    AvatarComponent,
    BadgeComponent
  ]
})
export class DefaultHeaderComponent extends HeaderComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  readonly #colorModeService = inject(ColorModeService);
  readonly colorMode = this.#colorModeService.colorMode;

  // Authentication properties
  currentUser: User | null = null;
  userInitials: string = '';
  pharmacyInfo: Pharmacy | null = null;
  userFullName: string = '';
  pharmacyName: string = '';

  // Notification properties
  notifications: Notification[] = [];
  unreadCount: number = 0;
  showNotificationDropdown: boolean = false;

  constructor(
    private authService: AuthService,
    private pharmacyService: PharmacyService,
    private notificationService: NotificationService
  ) {
    super();
  }

  ngOnInit(): void {
    // Subscribe to user changes
    this.authService.user$
      .pipe(takeUntil(this.destroy$))
      .subscribe(user => {
        this.currentUser = user;
        this.userInitials = this.generateUserInitials(user);

        // Fetch pharmacy info if user exists
        if (user && user.email) {
          this.fetchPharmacyInfo(user.email);
        }
      });

    // Subscribe to notifications
    this.notificationService.notifications$
      .pipe(takeUntil(this.destroy$))
      .subscribe(notifications => {
        this.notifications = notifications.slice(0, 5); // Show only latest 5 in dropdown
      });

    // Subscribe to unread count
    this.notificationService.unreadCount$
      .pipe(takeUntil(this.destroy$))
      .subscribe(count => {
        this.unreadCount = count;
      });
  }

  // Fetch pharmacy information by user email
  private fetchPharmacyInfo(email: string): void {
    this.pharmacyService.getPharmacyInfoByEmail(email)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (pharmacy) => {
          this.pharmacyInfo = pharmacy;
          this.userFullName = `${pharmacy.firstName} ${pharmacy.lastName}`;
          this.pharmacyName = pharmacy.pharmacyName || `Pharmacie ${pharmacy.firstName} ${pharmacy.lastName}`;

          // Regenerate user initials with the full name
          this.userInitials = this.generateUserInitials(this.currentUser);

          console.log('✅ Pharmacy info loaded:', pharmacy);
        },
        error: (error) => {
          console.error('❌ Failed to load pharmacy info:', error);
          // Fallback to email if pharmacy info fails
          this.userFullName = this.currentUser?.email || '';
          this.pharmacyName = 'Pharmacie';
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  logout(): void {
    this.authService.logout();
  }

  sidebarId = input('sidebar1');

  // Generate user initials for avatar
  private generateUserInitials(user: User | null): string {
    if (!user) return 'U';

    // Use full name if available from pharmacy info
    if (this.userFullName) {
      const nameParts = this.userFullName.split(' ');
      if (nameParts.length >= 2) {
        return (nameParts[0].charAt(0) + nameParts[1].charAt(0)).toUpperCase();
      }
      return nameParts[0].substring(0, 2).toUpperCase();
    }

    // Fallback to email
    if (user.email) {
      const emailParts = user.email.split('@')[0];
      if (emailParts.length >= 2) {
        return emailParts.substring(0, 2).toUpperCase();
      }
      return emailParts.charAt(0).toUpperCase();
    }

    return 'U';
  }



  // Notification methods
  toggleNotificationDropdown(): void {
    this.showNotificationDropdown = !this.showNotificationDropdown;
  }

  // Close notification panel when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const notificationWrapper = target.closest('.notification-wrapper');

    if (!notificationWrapper && this.showNotificationDropdown) {
      this.showNotificationDropdown = false;
    }
  }

  markNotificationAsRead(notification: Notification): void {
    if (notification.status === 'UNREAD') {
      this.notificationService.markAsRead(notification.id).subscribe({
        next: () => {
          console.log('✅ Notification marked as read:', notification.id);
        },
        error: (error) => {
          console.error('❌ Failed to mark notification as read:', error);
        }
      });
    }
  }

  markAllNotificationsAsRead(): void {
    this.notificationService.markAllAsRead().subscribe({
      next: () => {
        console.log('✅ All notifications marked as read');
      },
      error: (error) => {
        console.error('❌ Failed to mark all notifications as read:', error);
      }
    });
  }

  getNotificationTypeLabel(type: string): string {
    return NotificationTypeTranslations[type as keyof typeof NotificationTypeTranslations] || type;
  }

  getNotificationIcon(type: string): string {
    switch (type) {
      case 'PRESCRIPTION_ADDED':
        return 'cilMedicalCross';
      case 'PACKAGE_READY':
        return 'cilPackage';
      case 'DELIVERY_ASSIGNED':
        return 'cilTruck';
      case 'DELIVERY_COMPLETED':
        return 'cilCheckCircle';
      default:
        return 'cilBell';
    }
  }

  getNotificationColor(type: string): string {
    switch (type) {
      case 'PRESCRIPTION_ADDED':
        return '1061AC'; // Blue
      case 'PACKAGE_READY':
        return 'F39C12'; // Orange/Yellow
      case 'DELIVERY_ASSIGNED':
        return '57B6B1'; // Teal
      case 'DELIVERY_COMPLETED':
        return '27AE60'; // Green
      default:
        return '728A9B'; // Gray
    }
  }

  // Generate patient initials from notification data
  getPatientInitials(notification: Notification): string {
    // First priority: Check if notification has data with patientName
    if (notification.data && notification.data.patientName) {
      const patientName = notification.data.patientName.trim();

      // Split the patient name into parts
      const nameParts = patientName.split(/\s+/);

      if (nameParts.length >= 2) {
        // Get first letter of first name and first letter of last name
        const firstName = nameParts[0];
        const lastName = nameParts[1];
        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
      } else if (nameParts.length === 1) {
        // If only one name part, use first two letters
        const name = nameParts[0];
        return (name.charAt(0) + (name.charAt(1) || 'A')).toUpperCase();
      }
    }

    // Second priority: Try to extract from title or message (legacy support)
    const title = notification.title || '';
    const message = notification.message || '';

    const namePatterns = [
      /(?:de|pour|par)\s+([A-Za-zÀ-ÿ]+)\s+([A-Za-zÀ-ÿ]+)/i, // "de Azizi Souissi"
      /Patient:\s*([A-Za-zÀ-ÿ]+)\s+([A-Za-zÀ-ÿ]+)/i, // "Patient: Azizi Souissi"
      /([A-Za-zÀ-ÿ]+)\s+([A-Za-zÀ-ÿ]+)/i // General "FirstName LastName"
    ];

    for (const pattern of namePatterns) {
      const match = title.match(pattern) || message.match(pattern);
      if (match && match[1] && match[2]) {
        const firstName = match[1].trim();
        const lastName = match[2].trim();
        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
      }
    }

    // Final fallback: use notification type initials
    switch (notification.type) {
      case 'PRESCRIPTION_ADDED':
        return 'RX';
      case 'PACKAGE_READY':
        return 'PK';
      case 'DELIVERY_ASSIGNED':
        return 'DL';
      case 'DELIVERY_COMPLETED':
        return 'OK';
      default:
        return 'PT'; // Patient
    }
  }



  formatNotificationTime(date: string | Date): string {
    const notificationDate = new Date(date);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'À l\'instant';
    } else if (diffInMinutes < 60) {
      return `Il y a ${diffInMinutes} min`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `Il y a ${hours}h`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `Il y a ${days}j`;
    }
  }

  refreshNotifications(): void {
    this.notificationService.refreshNotifications();
  }

  public newMessages = [
    {
      id: 0,
      from: 'Jessica Williams',
      avatar: '7.jpg',
      status: 'success',
      title: 'Urgent: System Maintenance Tonight',
      time: 'Just now',
      link: 'apps/email/inbox/message',
      message: 'Attention team, we\'ll be conducting critical system maintenance tonight from 10 PM to 2 AM. Plan accordingly...'
    },
    {
      id: 1,
      from: 'Richard Johnson',
      avatar: '6.jpg',
      status: 'warning',
      title: 'Project Update: Milestone Achieved',
      time: '5 minutes ago',
      link: 'apps/email/inbox/message',
      message: 'Kudos on hitting sales targets last quarter! Let\'s keep the momentum. New goals, new victories ahead...'
    },
    {
      id: 2,
      from: 'Angela Rodriguez',
      avatar: '5.jpg',
      status: 'danger',
      title: 'Social Media Campaign Launch',
      time: '1:52 PM',
      link: 'apps/email/inbox/message',
      message: 'Exciting news! Our new social media campaign goes live tomorrow. Brace yourselves for engagement...'
    },
    {
      id: 3,
      from: 'Jane Lewis',
      avatar: '4.jpg',
      status: 'info',
      title: 'Inventory Checkpoint',
      time: '4:03 AM',
      link: 'apps/email/inbox/message',
      message: 'Team, it\'s time for our monthly inventory check. Accurate counts ensure smooth operations. Let\'s nail it...'
    },
    {
      id: 3,
      from: 'Ryan Miller',
      avatar: '4.jpg',
      status: 'info',
      title: 'Customer Feedback Results',
      time: '3 days ago',
      link: 'apps/email/inbox/message',
      message: 'Our latest customer feedback is in. Let\'s analyze and discuss improvements for an even better service...'
    }
  ];

  public newNotifications = [
    { id: 0, title: 'New user registered', icon: 'cilUserFollow', color: 'success' },
    { id: 1, title: 'User deleted', icon: 'cilUserUnfollow', color: 'danger' },
    { id: 2, title: 'Sales report is ready', icon: 'cilChartPie', color: 'info' },
    { id: 3, title: 'New client', icon: 'cilBasket', color: 'primary' },
    { id: 4, title: 'Server overloaded', icon: 'cilSpeedometer', color: 'warning' }
  ];

  public newStatus = [
    { id: 0, title: 'CPU Usage', value: 25, color: 'info', details: '348 Processes. 1/4 Cores.' },
    { id: 1, title: 'Memory Usage', value: 70, color: 'warning', details: '11444GB/16384MB' },
    { id: 2, title: 'SSD 1 Usage', value: 90, color: 'danger', details: '243GB/256GB' }
  ];

  public newTasks = [
    { id: 0, title: 'Upgrade NPM', value: 0, color: 'info' },
    { id: 1, title: 'ReactJS Version', value: 25, color: 'danger' },
    { id: 2, title: 'VueJS Version', value: 50, color: 'warning' },
    { id: 3, title: 'Add new layouts', value: 75, color: 'info' },
    { id: 4, title: 'Angular Version', value: 100, color: 'success' }
  ];

}
