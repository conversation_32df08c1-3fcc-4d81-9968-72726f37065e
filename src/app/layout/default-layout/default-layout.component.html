<!--sidebar-->
<c-sidebar
  #sidebar1="cSidebar"
  class="d-print-none sidebar sidebar-fixed border-end sidebar-with-footer"
  id="sidebar1"
  visible
  [narrow]="isSidebarNarrow"
  [unfoldable]="false"
  colorScheme="dark"
>
  <div class="sidebar-header custom-header border-bottom">
    <img src="assets/images/logo.png" alt="MED4 SOLUTIONS Logo" class="sidebar-brand-full sidebar-logo">
    <img src="assets/images/<EMAIL>" alt="MED4 SOLUTIONS Narrow Logo" class="sidebar-brand-narrow sidebar-logo">
    <span
      class="sidebar-toggler"
      role="button"
      aria-label="Toggle sidebar narrow mode"
      (click)="isSidebarNarrow = !isSidebarNarrow"
    >
      <img src="assets/images/narrow.svg" alt="Narrow Sidebar Icon">
    </span>
  </div>

  <div class="sidebar-content">
    <ng-scrollbar #scrollbar="ngScrollbar" (updated)="onScrollbarUpdate(scrollbar.state)" class="overflow" pointerEventsMethod="scrollbar" visibility="hover">
      <c-sidebar-nav #overflow [navItems]="navItems" compact />
    </ng-scrollbar>

    <!-- Emergency Hotline Footer -->
    <div class="emergency-hotline-footer">
      <div class="emergency-card">
        <div class="emergency-content">
          <div class="emergency-icon-container">
            <svg cIcon name="cil-phone" class="emergency-icon"></svg>
          </div>
          <div class="emergency-text">
            <div class="emergency-title">Emergency Hotlines:</div>
            <div class="emergency-phone">+234 92 928 2891</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</c-sidebar>

<!--main-->
<div class="wrapper d-flex flex-column min-vh-100">
  <!--app-header-->
  <app-default-header
    [cShadowOnScroll]="'sm'"
    class="mb-4 d-print-none header header-sticky p-0 shadow-sm"
    position="sticky"
    sidebarId="sidebar1"
  />
  <!--app-body-->
  <div class="body flex-grow-1">
    <c-container breakpoint="lg" class="h-auto px-4">
      <router-outlet />
    </c-container>
  </div>
  <!--app footer-->
  <app-default-footer />
</div>
