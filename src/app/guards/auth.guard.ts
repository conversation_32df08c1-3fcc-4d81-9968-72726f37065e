import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, map, take } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate, CanActivateChild {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    _route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkAuth(state.url);
  }

  canActivateChild(
    _childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkAuth(state.url);
  }

  private checkAuth(url: string): Observable<boolean> {
    return this.authService.authState$.pipe(
      take(1),
      map(authState => {
        if (!authState.isAuthenticated) {
          console.log('🔒 Access denied. User not authenticated. Redirecting to login...');
          this.router.navigate(['/login'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        // Check if user has permission to access the application (only roles 4 and 6)
        if (!this.authService.canAccessApplication()) {
          console.log('🔒 Access denied. User role not authorized for this application.');
          this.authService.logout(); // Log out users with unauthorized roles
          return false;
        }

        return true;
      })
    );
  }
}
