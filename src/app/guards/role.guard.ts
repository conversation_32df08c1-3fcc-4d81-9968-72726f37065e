import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, map, take } from 'rxjs';
import { AuthService } from '../services/auth.service';
import { Role } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate, CanActivateChild {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkRole(route);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkRole(childRoute);
  }

  private checkRole(route: ActivatedRouteSnapshot): Observable<boolean> {
    const requiredRoles = route.data['roles'] as Role[];
    
    if (!requiredRoles || requiredRoles.length === 0) {
      return this.authService.isAuthenticated$;
    }

    return this.authService.user$.pipe(
      take(1),
      map(user => {
        if (!user) {
          console.log('🔒 No user found. Redirecting to login...');
          this.router.navigate(['/login']);
          return false;
        }

        const hasRequiredRole = requiredRoles.includes(user.role);
        
        if (!hasRequiredRole) {
          console.log(`🔒 Access denied. Required roles: ${requiredRoles.join(', ')}, User role: ${user.role}`);
          this.router.navigate(['/403']); // Forbidden page
          return false;
        }

        return true;
      })
    );
  }
}
