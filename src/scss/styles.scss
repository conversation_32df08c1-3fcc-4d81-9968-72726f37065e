/* You can add global styles to this file, and also import other style files */

// If you want to override variables do it here
@import "variables";

// Import styles with default layout.
@import "@coreui/coreui/scss/coreui";

// Import Chart.js custom tooltips styles
@import "@coreui/chartjs/scss/coreui-chartjs";

// Custom styles for this theme
@import "theme";

// Some temp fixes
//@import "fixes";

// If you want to add custom CSS you can put it here.
@import "custom";

// Examples
// We use those styles to show code examples, you should remove them in your application.
@import "examples";

html, body { height: 100%; }
body { margin: 0; font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; }

/* Global Modal Styles for Package Creation */
.cdk-overlay-container {
  z-index: 1000 !important;
}

.cdk-overlay-backdrop {
  background: rgba(0, 0, 0, 0.6) !important;
  z-index: 999 !important;
}

.cdk-overlay-pane {
  z-index: 1001 !important;
}

.custom-package-modal .mat-mdc-dialog-container {
  padding: 20px !important;
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  overflow: visible !important;
  z-index: 1002 !important;
}

.custom-package-modal .mat-mdc-dialog-surface {
  padding: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  overflow: visible !important;
  z-index: 1003 !important;
}
