export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000', // Your NestJS backend URL
  uploadUrl: 'http://localhost:3000/uploads', // Direct backend URL for uploads
  fileBaseUrl: 'http://localhost:3000', // Direct file access
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  pagination: {
    defaultPageSize: 10,
    maxPageSize: 100
  },
  firebase: {
    apiKey: "AIzaSyCwCsfXRu7XLtULa_rAkMA0A-qsV1dYcAU", // You'll need to get this from Firebase Console
    authDomain: "med4-notifications.firebaseapp.com",
    projectId: "med4-notifications",
    storageBucket: "med4-notifications.appspot.com",
    messagingSenderId: "669793713512",
    appId: "1:669793713512:web:bfef6772ee48cd47dceadd", // You'll need to get this from Firebase Console
    vapidKey: "BFj-DARtW3YMcbx2jnv0-dgdCpt7P9bOlrVEdV4F09oH3g1PnBTuRi6vHK-ZJs4S9TMajshH5gdMhP34Vati09Y"
  }
};
