// Firebase messaging service worker
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX", // You'll need to get this from Firebase Console
  authDomain: "med4-notifications.firebaseapp.com",
  projectId: "med4-notifications",
  storageBucket: "med4-notifications.appspot.com",
  messagingSenderId: "669793713512",
  appId: "1:669793713512:web:XXXXXXXXXXXXXXXX" // You'll need to get this from Firebase Console
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging and get a reference to the service
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message:', payload);

  const notificationTitle = payload.notification?.title || 'Med4 Solutions';
  const notificationOptions = {
    body: payload.notification?.body || '',
    icon: '/assets/images/med4-logo-login.png',
    badge: '/assets/images/med4-logo-login.png',
    tag: payload.data?.notificationId || 'med4-notification',
    data: payload.data,
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: 'Voir'
      },
      {
        action: 'dismiss',
        title: 'Ignorer'
      }
    ]
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification click received:', event);

  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Handle notification click
  const data = event.notification.data;
  let url = '/dashboard';

  if (data?.type) {
    switch (data.type) {
      case 'PRESCRIPTION_ADDED':
        url = '/prescriptions';
        break;
      case 'PACKAGE_READY':
        url = '/orders';
        break;
      case 'DELIVERY_ASSIGNED':
      case 'DELIVERY_COMPLETED':
        url = '/orders';
        break;
      default:
        url = '/dashboard';
        break;
    }
  }

  // Open the app and navigate to the appropriate page
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url.includes(url) && 'focus' in client) {
          return client.focus();
        }
      }

      // If no window/tab is open, open a new one
      if (clients.openWindow) {
        return clients.openWindow(`/#${url}`);
      }
    })
  );
});
